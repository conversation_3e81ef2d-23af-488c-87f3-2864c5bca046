/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/projects/[id]/classify/page";
exports.ids = ["app/projects/[id]/classify/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fprojects%2F%5Bid%5D%2Fclassify%2Fpage&page=%2Fprojects%2F%5Bid%5D%2Fclassify%2Fpage&appPaths=%2Fprojects%2F%5Bid%5D%2Fclassify%2Fpage&pagePath=private-next-app-dir%2Fprojects%2F%5Bid%5D%2Fclassify%2Fpage.tsx&appDir=%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fprojects%2F%5Bid%5D%2Fclassify%2Fpage&page=%2Fprojects%2F%5Bid%5D%2Fclassify%2Fpage&appPaths=%2Fprojects%2F%5Bid%5D%2Fclassify%2Fpage&pagePath=private-next-app-dir%2Fprojects%2F%5Bid%5D%2Fclassify%2Fpage.tsx&appDir=%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/projects/[id]/classify/page.tsx */ \"(rsc)/./src/app/projects/[id]/classify/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'projects',\n        {\n        children: [\n        '[id]',\n        {\n        children: [\n        'classify',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"/Users/<USER>/Downloads/classify-test/frontend/src/app/projects/[id]/classify/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"/Users/<USER>/Downloads/classify-test/frontend/src/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Downloads/classify-test/frontend/src/app/projects/[id]/classify/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/projects/[id]/classify/page\",\n        pathname: \"/projects/[id]/classify\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fprojects%2F%5Bid%5D%2Fclassify%2Fpage&page=%2Fprojects%2F%5Bid%5D%2Fclassify%2Fpage&appPaths=%2Fprojects%2F%5Bid%5D%2Fclassify%2Fpage&pagePath=private-next-app-dir%2Fprojects%2F%5Bid%5D%2Fclassify%2Fpage.tsx&appDir=%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fsrc%2Fcomponents%2FClassificationInterface.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fsrc%2Fcomponents%2FClassificationInterface.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ClassificationInterface.tsx */ \"(rsc)/./src/components/ClassificationInterface.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGa2FkZG8lMkZEb3dubG9hZHMlMkZjbGFzc2lmeS10ZXN0JTJGZnJvbnRlbmQlMkZzcmMlMkZjb21wb25lbnRzJTJGQ2xhc3NpZmljYXRpb25JbnRlcmZhY2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb01BQTBKIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiL1VzZXJzL2thZGRvL0Rvd25sb2Fkcy9jbGFzc2lmeS10ZXN0L2Zyb250ZW5kL3NyYy9jb21wb25lbnRzL0NsYXNzaWZpY2F0aW9uSW50ZXJmYWNlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fsrc%2Fcomponents%2FClassificationInterface.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIi9Vc2Vycy9rYWRkby9Eb3dubG9hZHMvY2xhc3NpZnktdGVzdC9mcm9udGVuZC9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"719cb0fc3f63\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvVXNlcnMva2FkZG8vRG93bmxvYWRzL2NsYXNzaWZ5LXRlc3QvZnJvbnRlbmQvc3JjL2FwcC9nbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjcxOWNiMGZjM2Y2M1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n\nconst metadata = {\n    title: \"Image Classification Tool\",\n    description: \"A flexible web application for manual classification of images\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default().variable)} antialiased bg-gray-50 min-h-screen`,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                    className: \"bg-white shadow-sm border-b\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center h-16\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl font-semibold text-gray-900\",\n                                    children: \"Image Classification Tool\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/app/layout.tsx\",\n                                    lineNumber: 34,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/app/layout.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/app/layout.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/app/layout.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/app/layout.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/app/layout.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/app/layout.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/app/layout.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/projects/[id]/classify/page.tsx":
/*!*************************************************!*\
  !*** ./src/app/projects/[id]/classify/page.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ClassifyPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ClassificationInterface__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ClassificationInterface */ \"(rsc)/./src/components/ClassificationInterface.tsx\");\n\n\nfunction ClassifyPage({ params }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClassificationInterface__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        projectId: params.id\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/app/projects/[id]/classify/page.tsx\",\n        lineNumber: 10,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL3Byb2plY3RzL1tpZF0vY2xhc3NpZnkvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBMkU7QUFRNUQsU0FBU0MsYUFBYSxFQUFFQyxNQUFNLEVBQXFCO0lBQ2hFLHFCQUFPLDhEQUFDRiwyRUFBdUJBO1FBQUNHLFdBQVdELE9BQU9FLEVBQUU7Ozs7OztBQUN0RCIsInNvdXJjZXMiOlsiL1VzZXJzL2thZGRvL0Rvd25sb2Fkcy9jbGFzc2lmeS10ZXN0L2Zyb250ZW5kL3NyYy9hcHAvcHJvamVjdHMvW2lkXS9jbGFzc2lmeS9wYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgQ2xhc3NpZmljYXRpb25JbnRlcmZhY2UgZnJvbSAnQC9jb21wb25lbnRzL0NsYXNzaWZpY2F0aW9uSW50ZXJmYWNlJztcblxuaW50ZXJmYWNlIENsYXNzaWZ5UGFnZVByb3BzIHtcbiAgcGFyYW1zOiB7XG4gICAgaWQ6IHN0cmluZztcbiAgfTtcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQ2xhc3NpZnlQYWdlKHsgcGFyYW1zIH06IENsYXNzaWZ5UGFnZVByb3BzKSB7XG4gIHJldHVybiA8Q2xhc3NpZmljYXRpb25JbnRlcmZhY2UgcHJvamVjdElkPXtwYXJhbXMuaWR9IC8+O1xufVxuIl0sIm5hbWVzIjpbIkNsYXNzaWZpY2F0aW9uSW50ZXJmYWNlIiwiQ2xhc3NpZnlQYWdlIiwicGFyYW1zIiwicHJvamVjdElkIiwiaWQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/projects/[id]/classify/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/ClassificationInterface.tsx":
/*!****************************************************!*\
  !*** ./src/components/ClassificationInterface.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fsrc%2Fcomponents%2FClassificationInterface.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fsrc%2Fcomponents%2FClassificationInterface.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ClassificationInterface.tsx */ \"(ssr)/./src/components/ClassificationInterface.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGa2FkZG8lMkZEb3dubG9hZHMlMkZjbGFzc2lmeS10ZXN0JTJGZnJvbnRlbmQlMkZzcmMlMkZjb21wb25lbnRzJTJGQ2xhc3NpZmljYXRpb25JbnRlcmZhY2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb01BQTBKIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiL1VzZXJzL2thZGRvL0Rvd25sb2Fkcy9jbGFzc2lmeS10ZXN0L2Zyb250ZW5kL3NyYy9jb21wb25lbnRzL0NsYXNzaWZpY2F0aW9uSW50ZXJmYWNlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fsrc%2Fcomponents%2FClassificationInterface.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/ClassificationInterface.tsx":
/*!****************************************************!*\
  !*** ./src/components/ClassificationInterface.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ClassificationInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/api */ \"(ssr)/./src/services/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction ClassificationInterface({ projectId }) {\n    const [project, setProject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        currentImage: null,\n        images: [],\n        currentIndex: 0,\n        isLoading: true,\n        error: null\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ClassificationInterface.useEffect\": ()=>{\n            loadProject();\n            loadImages();\n        }\n    }[\"ClassificationInterface.useEffect\"], [\n        projectId\n    ]);\n    const loadProject = async ()=>{\n        try {\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_3__.apiService.getProject(projectId);\n            if (response.success) {\n                setProject(response.data);\n            } else {\n                setState((prev)=>({\n                        ...prev,\n                        error: response.error || 'Failed to load project'\n                    }));\n            }\n        } catch (err) {\n            setState((prev)=>({\n                    ...prev,\n                    error: 'Failed to load project'\n                }));\n            console.error('Error loading project:', err);\n        }\n    };\n    const loadImages = async ()=>{\n        try {\n            setState((prev)=>({\n                    ...prev,\n                    isLoading: true,\n                    error: null\n                }));\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_3__.apiService.getProjectImagesWithClassifications(projectId);\n            if (response.success && response.data) {\n                const images = response.data;\n                setState((prev)=>({\n                        ...prev,\n                        images,\n                        currentImage: images.length > 0 ? images[0] : null,\n                        currentIndex: 0,\n                        isLoading: false\n                    }));\n            } else {\n                setState((prev)=>({\n                        ...prev,\n                        error: response.error || 'Failed to load images',\n                        isLoading: false\n                    }));\n            }\n        } catch (err) {\n            setState((prev)=>({\n                    ...prev,\n                    error: 'Failed to load images',\n                    isLoading: false\n                }));\n            console.error('Error loading images:', err);\n        }\n    };\n    const classifyImage = async (className)=>{\n        if (!state.currentImage) return;\n        try {\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_3__.apiService.classifyImage({\n                image_id: state.currentImage.id,\n                class_name: className\n            });\n            if (response.success) {\n                // Update the current image with classification\n                const updatedImage = {\n                    ...state.currentImage,\n                    status: 'classified',\n                    classification: {\n                        id: response.data.id,\n                        image_id: state.currentImage.id,\n                        project_id: projectId,\n                        class_name: className,\n                        classified_at: new Date().toISOString()\n                    }\n                };\n                // Update images array\n                const updatedImages = state.images.map((img)=>img.id === state.currentImage.id ? updatedImage : img);\n                setState((prev)=>({\n                        ...prev,\n                        images: updatedImages,\n                        currentImage: updatedImage\n                    }));\n                // Auto-advance to next unclassified image\n                setTimeout(()=>{\n                    goToNextUnclassified();\n                }, 500);\n                // Refresh project stats\n                loadProject();\n            } else {\n                setState((prev)=>({\n                        ...prev,\n                        error: response.error || 'Failed to classify image'\n                    }));\n            }\n        } catch (err) {\n            setState((prev)=>({\n                    ...prev,\n                    error: 'Failed to classify image'\n                }));\n            console.error('Error classifying image:', err);\n        }\n    };\n    const removeClassification = async ()=>{\n        if (!state.currentImage || !state.currentImage.classification) return;\n        try {\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_3__.apiService.removeClassification(state.currentImage.id);\n            if (response.success) {\n                // Update the current image to remove classification\n                const updatedImage = {\n                    ...state.currentImage,\n                    status: 'unclassified',\n                    classification: undefined\n                };\n                // Update images array\n                const updatedImages = state.images.map((img)=>img.id === state.currentImage.id ? updatedImage : img);\n                setState((prev)=>({\n                        ...prev,\n                        images: updatedImages,\n                        currentImage: updatedImage\n                    }));\n                // Refresh project stats\n                loadProject();\n            } else {\n                setState((prev)=>({\n                        ...prev,\n                        error: response.error || 'Failed to remove classification'\n                    }));\n            }\n        } catch (err) {\n            setState((prev)=>({\n                    ...prev,\n                    error: 'Failed to remove classification'\n                }));\n            console.error('Error removing classification:', err);\n        }\n    };\n    const goToImage = (index)=>{\n        if (index >= 0 && index < state.images.length) {\n            setState((prev)=>({\n                    ...prev,\n                    currentIndex: index,\n                    currentImage: prev.images[index]\n                }));\n        }\n    };\n    const goToNextUnclassified = ()=>{\n        const nextUnclassified = state.images.findIndex((img, index)=>index > state.currentIndex && img.status === 'unclassified');\n        if (nextUnclassified !== -1) {\n            goToImage(nextUnclassified);\n        } else {\n            // Look from the beginning\n            const firstUnclassified = state.images.findIndex((img)=>img.status === 'unclassified');\n            if (firstUnclassified !== -1) {\n                goToImage(firstUnclassified);\n            }\n        }\n    };\n    const goToPrevious = ()=>{\n        if (state.currentIndex > 0) {\n            goToImage(state.currentIndex - 1);\n        }\n    };\n    const goToNext = ()=>{\n        if (state.currentIndex < state.images.length - 1) {\n            goToImage(state.currentIndex + 1);\n        }\n    };\n    // Keyboard navigation\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ClassificationInterface.useEffect\": ()=>{\n            const handleKeyPress = {\n                \"ClassificationInterface.useEffect.handleKeyPress\": (e)=>{\n                    if (!project) return;\n                    if (e.key >= '1' && e.key <= '9') {\n                        const classIndex = parseInt(e.key) - 1;\n                        if (classIndex < project.classes.length) {\n                            classifyImage(project.classes[classIndex]);\n                        }\n                    } else if (e.key === 'ArrowLeft') {\n                        goToPrevious();\n                    } else if (e.key === 'ArrowRight') {\n                        goToNext();\n                    } else if (e.key === 'Backspace' || e.key === 'Delete') {\n                        removeClassification();\n                    }\n                }\n            }[\"ClassificationInterface.useEffect.handleKeyPress\"];\n            window.addEventListener('keydown', handleKeyPress);\n            return ({\n                \"ClassificationInterface.useEffect\": ()=>window.removeEventListener('keydown', handleKeyPress)\n            })[\"ClassificationInterface.useEffect\"];\n        }\n    }[\"ClassificationInterface.useEffect\"], [\n        project,\n        state.currentIndex,\n        state.currentImage\n    ]);\n    if (state.isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                lineNumber: 222,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n            lineNumber: 221,\n            columnNumber: 7\n        }, this);\n    }\n    if (state.error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg\",\n            children: state.error\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n            lineNumber: 229,\n            columnNumber: 7\n        }, this);\n    }\n    if (!project) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                    children: \"Project not found\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                    lineNumber: 238,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    href: \"/\",\n                    className: \"text-blue-600 hover:text-blue-700\",\n                    children: \"Back to Projects\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                    lineNumber: 239,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n            lineNumber: 237,\n            columnNumber: 7\n        }, this);\n    }\n    if (state.images.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-gray-400 text-6xl mb-4\",\n                    children: \"\\uD83D\\uDCF7\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                    lineNumber: 249,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                    children: \"No images to classify\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                    lineNumber: 250,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600 mb-6\",\n                    children: \"Upload images to this project to start classifying\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                    lineNumber: 251,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    href: `/projects/${projectId}`,\n                    className: \"bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors\",\n                    children: \"Back to Project\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                    lineNumber: 254,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n            lineNumber: 248,\n            columnNumber: 7\n        }, this);\n    }\n    const unclassifiedCount = state.images.filter((img)=>img.status === 'unclassified').length;\n    const progressPercentage = Math.round((state.images.length - unclassifiedCount) / state.images.length * 100);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-6xl mx-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: `/projects/${projectId}`,\n                                className: \"text-blue-600 hover:text-blue-700 mb-2 inline-block\",\n                                children: [\n                                    \"← Back to \",\n                                    project.name\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"Classification Interface\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                lineNumber: 278,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                        lineNumber: 271,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-right\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: [\n                                    \"Image \",\n                                    state.currentIndex + 1,\n                                    \" of \",\n                                    state.images.length\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: [\n                                    unclassifiedCount,\n                                    \" remaining • \",\n                                    progressPercentage,\n                                    \"% complete\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                lineNumber: 284,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                        lineNumber: 280,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                lineNumber: 270,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full bg-gray-200 rounded-full h-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-green-600 h-2 rounded-full transition-all duration-300\",\n                        style: {\n                            width: `${progressPercentage}%`\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                        lineNumber: 293,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                    lineNumber: 292,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                lineNumber: 291,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2\",\n                        children: state.currentImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"aspect-square bg-gray-100 rounded-lg overflow-hidden mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: _services_api__WEBPACK_IMPORTED_MODULE_3__.apiService.getImageUrl(state.currentImage.file_path),\n                                        alt: state.currentImage.original_filename,\n                                        className: \"w-full h-full object-contain\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-medium text-gray-900\",\n                                                    children: state.currentImage.original_filename\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 19\n                                                }, this),\n                                                state.currentImage.classification && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-green-600 mt-1\",\n                                                    children: [\n                                                        \"Classified as: \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: state.currentImage.classification.class_name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                                            lineNumber: 318,\n                                                            columnNumber: 38\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 17\n                                        }, this),\n                                        state.currentImage.classification && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: removeClassification,\n                                            className: \"text-red-600 hover:text-red-700 text-sm\",\n                                            children: \"Remove Classification\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: goToPrevious,\n                                            disabled: state.currentIndex === 0,\n                                            className: \"bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n                                            children: \"← Previous\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: goToNextUnclassified,\n                                            className: \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors\",\n                                            children: \"Next Unclassified\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: goToNext,\n                                            disabled: state.currentIndex === state.images.length - 1,\n                                            className: \"bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n                                            children: \"Next →\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                            lineNumber: 349,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                            lineNumber: 304,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                        lineNumber: 302,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                        children: \"Classify Image\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: project.classes.map((className, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>classifyImage(className),\n                                                className: `w-full text-left px-4 py-3 rounded-lg font-medium transition-colors ${state.currentImage?.classification?.class_name === className ? 'bg-green-600 text-white' : 'bg-gray-100 hover:bg-gray-200 text-gray-900'}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-500 mr-2\",\n                                                        children: [\n                                                            \"(\",\n                                                            index + 1,\n                                                            \")\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    className\n                                                ]\n                                            }, className, true, {\n                                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 text-xs text-gray-500\",\n                                        children: [\n                                            \"Use keyboard shortcuts: 1-\",\n                                            project.classes.length,\n                                            \" to classify, ← → to navigate, Backspace to remove\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                        lineNumber: 382,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                lineNumber: 364,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                        children: \"All Images\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-4 gap-2 max-h-96 overflow-y-auto\",\n                                        children: state.images.map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>goToImage(index),\n                                                className: `aspect-square rounded overflow-hidden border-2 transition-colors ${index === state.currentIndex ? 'border-blue-500' : image.status === 'classified' ? 'border-green-500' : 'border-gray-200'}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: _services_api__WEBPACK_IMPORTED_MODULE_3__.apiService.getImageUrl(image.file_path),\n                                                        alt: image.original_filename,\n                                                        className: \"w-full h-full object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                                        lineNumber: 403,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    image.status === 'classified' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 bg-green-600 bg-opacity-20 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-green-600 text-white text-xs px-1 rounded\",\n                                                            children: \"✓\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                                            lineNumber: 410,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                                        lineNumber: 409,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, image.id, true, {\n                                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                                lineNumber: 392,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                        lineNumber: 390,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                lineNumber: 388,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                        lineNumber: 362,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                lineNumber: 300,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n        lineNumber: 268,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ClassificationInterface.tsx\n");

/***/ }),

/***/ "(ssr)/./src/services/api.ts":
/*!*****************************!*\
  !*** ./src/services/api.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiService: () => (/* binding */ apiService)\n/* harmony export */ });\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api';\nclass ApiService {\n    async request(endpoint, options = {}) {\n        const url = `${API_BASE_URL}${endpoint}`;\n        try {\n            const response = await fetch(url, {\n                headers: {\n                    'Content-Type': 'application/json',\n                    ...options.headers\n                },\n                ...options\n            });\n            if (!response.ok) {\n                throw new Error(`HTTP error! status: ${response.status}`);\n            }\n            return await response.json();\n        } catch (error) {\n            console.error('API request failed:', error);\n            throw error;\n        }\n    }\n    // Project endpoints\n    async getProjects() {\n        return this.request('/projects');\n    }\n    async getProject(id) {\n        return this.request(`/projects/${id}`);\n    }\n    async createProject(data) {\n        return this.request('/projects', {\n            method: 'POST',\n            body: JSON.stringify(data)\n        });\n    }\n    async updateProject(id, data) {\n        return this.request(`/projects/${id}`, {\n            method: 'PUT',\n            body: JSON.stringify(data)\n        });\n    }\n    async deleteProject(id) {\n        return this.request(`/projects/${id}`, {\n            method: 'DELETE'\n        });\n    }\n    // Upload endpoint\n    async uploadZip(projectId, file) {\n        const formData = new FormData();\n        formData.append('zipFile', file);\n        try {\n            const response = await fetch(`${API_BASE_URL}/projects/${projectId}/upload`, {\n                method: 'POST',\n                body: formData\n            });\n            if (!response.ok) {\n                throw new Error(`HTTP error! status: ${response.status}`);\n            }\n            return await response.json();\n        } catch (error) {\n            console.error('Upload failed:', error);\n            throw error;\n        }\n    }\n    // Image endpoints\n    async getProjectImages(projectId, status) {\n        const params = status ? `?status=${status}` : '';\n        return this.request(`/images/project/${projectId}${params}`);\n    }\n    async getProjectImagesWithClassifications(projectId) {\n        return this.request(`/images/project/${projectId}/with-classifications`);\n    }\n    async getNextUnclassifiedImage(projectId, currentImageId) {\n        const params = currentImageId ? `?currentImageId=${currentImageId}` : '';\n        return this.request(`/images/project/${projectId}/next-unclassified${params}`);\n    }\n    // Classification endpoints\n    async classifyImage(data) {\n        return this.request('/classifications', {\n            method: 'POST',\n            body: JSON.stringify(data)\n        });\n    }\n    async removeClassification(imageId) {\n        return this.request(`/classifications/image/${imageId}`, {\n            method: 'DELETE'\n        });\n    }\n    async getClassificationStats(projectId) {\n        return this.request(`/classifications/project/${projectId}/stats`);\n    }\n    // Export endpoint\n    async exportProject(projectId) {\n        try {\n            const response = await fetch(`${API_BASE_URL}/projects/${projectId}/export`);\n            if (!response.ok) {\n                throw new Error(`HTTP error! status: ${response.status}`);\n            }\n            // Get filename from Content-Disposition header or use default\n            const contentDisposition = response.headers.get('Content-Disposition');\n            let filename = 'export.zip';\n            if (contentDisposition) {\n                const filenameMatch = contentDisposition.match(/filename=\"(.+)\"/);\n                if (filenameMatch) {\n                    filename = filenameMatch[1];\n                }\n            }\n            // Create blob and download\n            const blob = await response.blob();\n            const url = window.URL.createObjectURL(blob);\n            const a = document.createElement('a');\n            a.href = url;\n            a.download = filename;\n            document.body.appendChild(a);\n            a.click();\n            window.URL.revokeObjectURL(url);\n            document.body.removeChild(a);\n        } catch (error) {\n            console.error('Export failed:', error);\n            throw error;\n        }\n    }\n    // Utility method to get image URL\n    getImageUrl(imagePath) {\n        return `${API_BASE_URL.replace('/api', '')}/uploads/${imagePath}`;\n    }\n}\nconst apiService = new ApiService();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/services/api.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fprojects%2F%5Bid%5D%2Fclassify%2Fpage&page=%2Fprojects%2F%5Bid%5D%2Fclassify%2Fpage&appPaths=%2Fprojects%2F%5Bid%5D%2Fclassify%2Fpage&pagePath=private-next-app-dir%2Fprojects%2F%5Bid%5D%2Fclassify%2Fpage.tsx&appDir=%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();