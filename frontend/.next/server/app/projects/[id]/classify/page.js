/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/projects/[id]/classify/page";
exports.ids = ["app/projects/[id]/classify/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fprojects%2F%5Bid%5D%2Fclassify%2Fpage&page=%2Fprojects%2F%5Bid%5D%2Fclassify%2Fpage&appPaths=%2Fprojects%2F%5Bid%5D%2Fclassify%2Fpage&pagePath=private-next-app-dir%2Fprojects%2F%5Bid%5D%2Fclassify%2Fpage.tsx&appDir=%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fprojects%2F%5Bid%5D%2Fclassify%2Fpage&page=%2Fprojects%2F%5Bid%5D%2Fclassify%2Fpage&appPaths=%2Fprojects%2F%5Bid%5D%2Fclassify%2Fpage&pagePath=private-next-app-dir%2Fprojects%2F%5Bid%5D%2Fclassify%2Fpage.tsx&appDir=%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/projects/[id]/classify/page.tsx */ \"(rsc)/./src/app/projects/[id]/classify/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'projects',\n        {\n        children: [\n        '[id]',\n        {\n        children: [\n        'classify',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"/Users/<USER>/Downloads/classify-test/frontend/src/app/projects/[id]/classify/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"/Users/<USER>/Downloads/classify-test/frontend/src/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Downloads/classify-test/frontend/src/app/projects/[id]/classify/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/projects/[id]/classify/page\",\n        pathname: \"/projects/[id]/classify\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fprojects%2F%5Bid%5D%2Fclassify%2Fpage&page=%2Fprojects%2F%5Bid%5D%2Fclassify%2Fpage&appPaths=%2Fprojects%2F%5Bid%5D%2Fclassify%2Fpage&pagePath=private-next-app-dir%2Fprojects%2F%5Bid%5D%2Fclassify%2Fpage.tsx&appDir=%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fsrc%2Fcomponents%2FClassificationInterface.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fsrc%2Fcomponents%2FClassificationInterface.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ClassificationInterface.tsx */ \"(rsc)/./src/components/ClassificationInterface.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGa2FkZG8lMkZEb3dubG9hZHMlMkZjbGFzc2lmeS10ZXN0JTJGZnJvbnRlbmQlMkZzcmMlMkZjb21wb25lbnRzJTJGQ2xhc3NpZmljYXRpb25JbnRlcmZhY2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb01BQTBKIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiL1VzZXJzL2thZGRvL0Rvd25sb2Fkcy9jbGFzc2lmeS10ZXN0L2Zyb250ZW5kL3NyYy9jb21wb25lbnRzL0NsYXNzaWZpY2F0aW9uSW50ZXJmYWNlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fsrc%2Fcomponents%2FClassificationInterface.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIi9Vc2Vycy9rYWRkby9Eb3dubG9hZHMvY2xhc3NpZnktdGVzdC9mcm9udGVuZC9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"719cb0fc3f63\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvVXNlcnMva2FkZG8vRG93bmxvYWRzL2NsYXNzaWZ5LXRlc3QvZnJvbnRlbmQvc3JjL2FwcC9nbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjcxOWNiMGZjM2Y2M1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n\nconst metadata = {\n    title: \"Image Classification Tool\",\n    description: \"A flexible web application for manual classification of images\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default().variable)} antialiased bg-gray-50 min-h-screen`,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                    className: \"bg-white shadow-sm border-b\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center h-16\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl font-semibold text-gray-900\",\n                                    children: \"Image Classification Tool\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/app/layout.tsx\",\n                                    lineNumber: 34,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/app/layout.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/app/layout.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/app/layout.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/app/layout.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/app/layout.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/app/layout.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/app/layout.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/projects/[id]/classify/page.tsx":
/*!*************************************************!*\
  !*** ./src/app/projects/[id]/classify/page.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ClassifyPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ClassificationInterface__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ClassificationInterface */ \"(rsc)/./src/components/ClassificationInterface.tsx\");\n\n\nasync function ClassifyPage({ params }) {\n    const { id } = await params;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClassificationInterface__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        projectId: id\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/app/projects/[id]/classify/page.tsx\",\n        lineNumber: 11,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL3Byb2plY3RzL1tpZF0vY2xhc3NpZnkvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBMkU7QUFRNUQsZUFBZUMsYUFBYSxFQUFFQyxNQUFNLEVBQXFCO0lBQ3RFLE1BQU0sRUFBRUMsRUFBRSxFQUFFLEdBQUcsTUFBTUQ7SUFDckIscUJBQU8sOERBQUNGLDJFQUF1QkE7UUFBQ0ksV0FBV0Q7Ozs7OztBQUM3QyIsInNvdXJjZXMiOlsiL1VzZXJzL2thZGRvL0Rvd25sb2Fkcy9jbGFzc2lmeS10ZXN0L2Zyb250ZW5kL3NyYy9hcHAvcHJvamVjdHMvW2lkXS9jbGFzc2lmeS9wYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgQ2xhc3NpZmljYXRpb25JbnRlcmZhY2UgZnJvbSAnQC9jb21wb25lbnRzL0NsYXNzaWZpY2F0aW9uSW50ZXJmYWNlJztcblxuaW50ZXJmYWNlIENsYXNzaWZ5UGFnZVByb3BzIHtcbiAgcGFyYW1zOiBQcm9taXNlPHtcbiAgICBpZDogc3RyaW5nO1xuICB9Pjtcbn1cblxuZXhwb3J0IGRlZmF1bHQgYXN5bmMgZnVuY3Rpb24gQ2xhc3NpZnlQYWdlKHsgcGFyYW1zIH06IENsYXNzaWZ5UGFnZVByb3BzKSB7XG4gIGNvbnN0IHsgaWQgfSA9IGF3YWl0IHBhcmFtcztcbiAgcmV0dXJuIDxDbGFzc2lmaWNhdGlvbkludGVyZmFjZSBwcm9qZWN0SWQ9e2lkfSAvPjtcbn1cbiJdLCJuYW1lcyI6WyJDbGFzc2lmaWNhdGlvbkludGVyZmFjZSIsIkNsYXNzaWZ5UGFnZSIsInBhcmFtcyIsImlkIiwicHJvamVjdElkIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/projects/[id]/classify/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/ClassificationInterface.tsx":
/*!****************************************************!*\
  !*** ./src/components/ClassificationInterface.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fsrc%2Fcomponents%2FClassificationInterface.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fsrc%2Fcomponents%2FClassificationInterface.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ClassificationInterface.tsx */ \"(ssr)/./src/components/ClassificationInterface.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGa2FkZG8lMkZEb3dubG9hZHMlMkZjbGFzc2lmeS10ZXN0JTJGZnJvbnRlbmQlMkZzcmMlMkZjb21wb25lbnRzJTJGQ2xhc3NpZmljYXRpb25JbnRlcmZhY2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb01BQTBKIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiL1VzZXJzL2thZGRvL0Rvd25sb2Fkcy9jbGFzc2lmeS10ZXN0L2Zyb250ZW5kL3NyYy9jb21wb25lbnRzL0NsYXNzaWZpY2F0aW9uSW50ZXJmYWNlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fsrc%2Fcomponents%2FClassificationInterface.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/ClassificationInterface.tsx":
/*!****************************************************!*\
  !*** ./src/components/ClassificationInterface.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ClassificationInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/api */ \"(ssr)/./src/services/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction ClassificationInterface({ projectId }) {\n    const [project, setProject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        currentImage: null,\n        images: [],\n        currentIndex: 0,\n        isLoading: true,\n        error: null\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ClassificationInterface.useEffect\": ()=>{\n            loadProject();\n            loadImages();\n        }\n    }[\"ClassificationInterface.useEffect\"], [\n        projectId\n    ]);\n    const loadProject = async ()=>{\n        try {\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_3__.apiService.getProject(projectId);\n            if (response.success) {\n                setProject(response.data);\n            } else {\n                setState((prev)=>({\n                        ...prev,\n                        error: response.error || 'Failed to load project'\n                    }));\n            }\n        } catch (err) {\n            setState((prev)=>({\n                    ...prev,\n                    error: 'Failed to load project'\n                }));\n            console.error('Error loading project:', err);\n        }\n    };\n    const loadImages = async ()=>{\n        try {\n            setState((prev)=>({\n                    ...prev,\n                    isLoading: true,\n                    error: null\n                }));\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_3__.apiService.getProjectImagesWithClassifications(projectId);\n            if (response.success && response.data) {\n                const images = response.data;\n                setState((prev)=>({\n                        ...prev,\n                        images,\n                        currentImage: images.length > 0 ? images[0] : null,\n                        currentIndex: 0,\n                        isLoading: false\n                    }));\n            } else {\n                setState((prev)=>({\n                        ...prev,\n                        error: response.error || 'Failed to load images',\n                        isLoading: false\n                    }));\n            }\n        } catch (err) {\n            setState((prev)=>({\n                    ...prev,\n                    error: 'Failed to load images',\n                    isLoading: false\n                }));\n            console.error('Error loading images:', err);\n        }\n    };\n    const classifyImage = async (className)=>{\n        if (!state.currentImage) return;\n        // Check if image is already classified with the same class\n        if (state.currentImage.classification?.class_name === className) {\n            return; // Already classified with this class, do nothing\n        }\n        try {\n            setState((prev)=>({\n                    ...prev,\n                    error: null\n                })); // Clear any previous errors\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_3__.apiService.classifyImage({\n                image_id: state.currentImage.id,\n                class_name: className\n            });\n            if (response.success) {\n                // Update the current image with classification\n                const updatedImage = {\n                    ...state.currentImage,\n                    status: 'classified',\n                    classification: {\n                        id: response.data.id,\n                        image_id: state.currentImage.id,\n                        project_id: projectId,\n                        class_name: className,\n                        classified_at: new Date().toISOString()\n                    }\n                };\n                // Update images array\n                const updatedImages = state.images.map((img)=>img.id === state.currentImage.id ? updatedImage : img);\n                setState((prev)=>({\n                        ...prev,\n                        images: updatedImages,\n                        currentImage: updatedImage,\n                        error: null\n                    }));\n                // Auto-advance to next unclassified image after a short delay\n                setTimeout(()=>{\n                    goToNextUnclassified();\n                }, 300);\n                // Refresh project stats\n                loadProject();\n            } else {\n                setState((prev)=>({\n                        ...prev,\n                        error: response.error || 'Failed to classify image'\n                    }));\n            }\n        } catch (err) {\n            setState((prev)=>({\n                    ...prev,\n                    error: 'Failed to classify image'\n                }));\n            console.error('Error classifying image:', err);\n        }\n    };\n    const removeClassification = async ()=>{\n        if (!state.currentImage || !state.currentImage.classification) return;\n        try {\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_3__.apiService.removeClassification(state.currentImage.id);\n            if (response.success) {\n                // Update the current image to remove classification\n                const updatedImage = {\n                    ...state.currentImage,\n                    status: 'unclassified',\n                    classification: undefined\n                };\n                // Update images array\n                const updatedImages = state.images.map((img)=>img.id === state.currentImage.id ? updatedImage : img);\n                setState((prev)=>({\n                        ...prev,\n                        images: updatedImages,\n                        currentImage: updatedImage\n                    }));\n                // Refresh project stats\n                loadProject();\n            } else {\n                setState((prev)=>({\n                        ...prev,\n                        error: response.error || 'Failed to remove classification'\n                    }));\n            }\n        } catch (err) {\n            setState((prev)=>({\n                    ...prev,\n                    error: 'Failed to remove classification'\n                }));\n            console.error('Error removing classification:', err);\n        }\n    };\n    const goToImage = (index)=>{\n        if (index >= 0 && index < state.images.length) {\n            setState((prev)=>({\n                    ...prev,\n                    currentIndex: index,\n                    currentImage: prev.images[index]\n                }));\n        }\n    };\n    const goToNextUnclassified = ()=>{\n        const nextUnclassified = state.images.findIndex((img, index)=>index > state.currentIndex && img.status === 'unclassified');\n        if (nextUnclassified !== -1) {\n            goToImage(nextUnclassified);\n        } else {\n            // Look from the beginning\n            const firstUnclassified = state.images.findIndex((img)=>img.status === 'unclassified');\n            if (firstUnclassified !== -1) {\n                goToImage(firstUnclassified);\n            }\n        }\n    };\n    const goToPrevious = ()=>{\n        if (state.currentIndex > 0) {\n            goToImage(state.currentIndex - 1);\n        }\n    };\n    const goToNext = ()=>{\n        if (state.currentIndex < state.images.length - 1) {\n            goToImage(state.currentIndex + 1);\n        }\n    };\n    // Keyboard navigation\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ClassificationInterface.useEffect\": ()=>{\n            const handleKeyPress = {\n                \"ClassificationInterface.useEffect.handleKeyPress\": (e)=>{\n                    if (!project) return;\n                    if (e.key >= '1' && e.key <= '9') {\n                        const classIndex = parseInt(e.key) - 1;\n                        if (classIndex < project.classes.length) {\n                            classifyImage(project.classes[classIndex]);\n                        }\n                    } else if (e.key === 'ArrowLeft') {\n                        goToPrevious();\n                    } else if (e.key === 'ArrowRight') {\n                        goToNext();\n                    } else if (e.key === 'Backspace' || e.key === 'Delete') {\n                        removeClassification();\n                    }\n                }\n            }[\"ClassificationInterface.useEffect.handleKeyPress\"];\n            window.addEventListener('keydown', handleKeyPress);\n            return ({\n                \"ClassificationInterface.useEffect\": ()=>window.removeEventListener('keydown', handleKeyPress)\n            })[\"ClassificationInterface.useEffect\"];\n        }\n    }[\"ClassificationInterface.useEffect\"], [\n        project,\n        state.currentIndex,\n        state.currentImage\n    ]);\n    if (state.isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                lineNumber: 230,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n            lineNumber: 229,\n            columnNumber: 7\n        }, this);\n    }\n    if (state.error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg\",\n            children: state.error\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n            lineNumber: 237,\n            columnNumber: 7\n        }, this);\n    }\n    if (!project) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                    children: \"Project not found\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                    lineNumber: 246,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    href: \"/\",\n                    className: \"text-blue-600 hover:text-blue-700\",\n                    children: \"Back to Projects\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                    lineNumber: 247,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n            lineNumber: 245,\n            columnNumber: 7\n        }, this);\n    }\n    if (state.images.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-gray-400 text-6xl mb-4\",\n                    children: \"\\uD83D\\uDCF7\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                    lineNumber: 257,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                    children: \"No images to classify\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                    lineNumber: 258,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600 mb-6\",\n                    children: \"Upload images to this project to start classifying\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                    lineNumber: 259,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    href: `/projects/${projectId}`,\n                    className: \"bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors\",\n                    children: \"Back to Project\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                    lineNumber: 262,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n            lineNumber: 256,\n            columnNumber: 7\n        }, this);\n    }\n    const unclassifiedCount = state.images.filter((img)=>img.status === 'unclassified').length;\n    const progressPercentage = Math.round((state.images.length - unclassifiedCount) / state.images.length * 100);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-6xl mx-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: `/projects/${projectId}`,\n                                className: \"text-blue-600 hover:text-blue-700 mb-2 inline-block\",\n                                children: [\n                                    \"← Back to \",\n                                    project.name\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                lineNumber: 280,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"Classification Interface\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                        lineNumber: 279,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-right\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: [\n                                    \"Image \",\n                                    state.currentIndex + 1,\n                                    \" of \",\n                                    state.images.length\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: [\n                                    unclassifiedCount,\n                                    \" remaining • \",\n                                    progressPercentage,\n                                    \"% complete\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                lineNumber: 278,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full bg-gray-200 rounded-full h-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-green-600 h-2 rounded-full transition-all duration-300\",\n                        style: {\n                            width: `${progressPercentage}%`\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                        lineNumber: 301,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                    lineNumber: 300,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                lineNumber: 299,\n                columnNumber: 7\n            }, this),\n            state.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6\",\n                children: [\n                    state.error,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setState((prev)=>({\n                                    ...prev,\n                                    error: null\n                                })),\n                        className: \"ml-2 text-red-500 hover:text-red-700\",\n                        children: \"\\xd7\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                        lineNumber: 312,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                lineNumber: 310,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2\",\n                        children: state.currentImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"aspect-square bg-gray-100 rounded-lg overflow-hidden mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: _services_api__WEBPACK_IMPORTED_MODULE_3__.apiService.getImageUrl(state.currentImage.file_path),\n                                        alt: state.currentImage.original_filename,\n                                        className: \"w-full h-full object-contain\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium text-gray-900\",\n                                                children: state.currentImage.original_filename\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                                lineNumber: 336,\n                                                columnNumber: 19\n                                            }, this),\n                                            state.currentImage.classification && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-green-600 mt-1\",\n                                                children: [\n                                                    \"Classified as: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: state.currentImage.classification.class_name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 38\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                    lineNumber: 334,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: goToPrevious,\n                                            disabled: state.currentIndex === 0,\n                                            className: \"bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n                                            children: \"← Previous\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: goToNextUnclassified,\n                                            className: \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors\",\n                                            children: \"Next Unclassified\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: goToNext,\n                                            disabled: state.currentIndex === state.images.length - 1,\n                                            className: \"bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n                                            children: \"Next →\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                    lineNumber: 347,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                            lineNumber: 325,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                        lineNumber: 323,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                        children: \"Classify Image\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                        lineNumber: 379,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: project.classes.map((className, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>classifyImage(className),\n                                                className: `w-full text-left px-4 py-3 rounded-lg font-medium transition-colors ${state.currentImage?.classification?.class_name === className ? 'bg-green-600 text-white' : 'bg-gray-100 hover:bg-gray-200 text-gray-900'}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-500 mr-2\",\n                                                        children: [\n                                                            \"(\",\n                                                            index + 1,\n                                                            \")\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                                        lineNumber: 391,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    className\n                                                ]\n                                            }, className, true, {\n                                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 text-xs text-gray-500\",\n                                        children: [\n                                            \"Use keyboard shortcuts: 1-\",\n                                            project.classes.length,\n                                            \" to classify, ← → to navigate, Backspace to remove\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                        lineNumber: 396,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                lineNumber: 378,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                        children: \"All Images\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-4 gap-2 max-h-96 overflow-y-auto\",\n                                        children: state.images.map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>goToImage(index),\n                                                className: `relative aspect-square rounded overflow-hidden border-2 transition-colors ${index === state.currentIndex ? 'border-blue-500' : image.status === 'classified' ? 'border-green-500' : 'border-gray-200'}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: _services_api__WEBPACK_IMPORTED_MODULE_3__.apiService.getImageUrl(image.file_path),\n                                                        alt: image.original_filename,\n                                                        className: \"w-full h-full object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                                        lineNumber: 417,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    image.status === 'classified' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute top-1 right-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-green-600 text-white text-xs px-1 py-0.5 rounded\",\n                                                            children: \"✓\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                                            lineNumber: 424,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                                        lineNumber: 423,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, image.id, true, {\n                                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                lineNumber: 402,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                        lineNumber: 376,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                lineNumber: 321,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n        lineNumber: 276,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ClassificationInterface.tsx\n");

/***/ }),

/***/ "(ssr)/./src/services/api.ts":
/*!*****************************!*\
  !*** ./src/services/api.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiService: () => (/* binding */ apiService)\n/* harmony export */ });\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api';\nclass ApiService {\n    async request(endpoint, options = {}) {\n        const url = `${API_BASE_URL}${endpoint}`;\n        try {\n            const response = await fetch(url, {\n                headers: {\n                    'Content-Type': 'application/json',\n                    ...options.headers\n                },\n                ...options\n            });\n            if (!response.ok) {\n                throw new Error(`HTTP error! status: ${response.status}`);\n            }\n            return await response.json();\n        } catch (error) {\n            console.error('API request failed:', error);\n            throw error;\n        }\n    }\n    // Project endpoints\n    async getProjects() {\n        return this.request('/projects');\n    }\n    async getProject(id) {\n        return this.request(`/projects/${id}`);\n    }\n    async createProject(data) {\n        return this.request('/projects', {\n            method: 'POST',\n            body: JSON.stringify(data)\n        });\n    }\n    async updateProject(id, data) {\n        return this.request(`/projects/${id}`, {\n            method: 'PUT',\n            body: JSON.stringify(data)\n        });\n    }\n    async deleteProject(id) {\n        return this.request(`/projects/${id}`, {\n            method: 'DELETE'\n        });\n    }\n    // Upload endpoint\n    async uploadZip(projectId, file) {\n        const formData = new FormData();\n        formData.append('zipFile', file);\n        try {\n            const response = await fetch(`${API_BASE_URL}/projects/${projectId}/upload`, {\n                method: 'POST',\n                body: formData\n            });\n            if (!response.ok) {\n                throw new Error(`HTTP error! status: ${response.status}`);\n            }\n            return await response.json();\n        } catch (error) {\n            console.error('Upload failed:', error);\n            throw error;\n        }\n    }\n    // Image endpoints\n    async getProjectImages(projectId, status) {\n        const params = status ? `?status=${status}` : '';\n        return this.request(`/images/project/${projectId}${params}`);\n    }\n    async getProjectImagesWithClassifications(projectId) {\n        return this.request(`/images/project/${projectId}/with-classifications`);\n    }\n    async getNextUnclassifiedImage(projectId, currentImageId) {\n        const params = currentImageId ? `?currentImageId=${currentImageId}` : '';\n        return this.request(`/images/project/${projectId}/next-unclassified${params}`);\n    }\n    // Classification endpoints\n    async classifyImage(data) {\n        return this.request('/classifications', {\n            method: 'POST',\n            body: JSON.stringify(data)\n        });\n    }\n    async removeClassification(imageId) {\n        return this.request(`/classifications/image/${imageId}`, {\n            method: 'DELETE'\n        });\n    }\n    async getClassificationStats(projectId) {\n        return this.request(`/classifications/project/${projectId}/stats`);\n    }\n    // Export endpoint\n    async exportProject(projectId) {\n        try {\n            const response = await fetch(`${API_BASE_URL}/projects/${projectId}/export`);\n            if (!response.ok) {\n                throw new Error(`HTTP error! status: ${response.status}`);\n            }\n            // Get filename from Content-Disposition header or use default\n            const contentDisposition = response.headers.get('Content-Disposition');\n            let filename = 'export.zip';\n            if (contentDisposition) {\n                const filenameMatch = contentDisposition.match(/filename=\"(.+)\"/);\n                if (filenameMatch) {\n                    filename = filenameMatch[1];\n                }\n            }\n            // Create blob and download\n            const blob = await response.blob();\n            const url = window.URL.createObjectURL(blob);\n            const a = document.createElement('a');\n            a.href = url;\n            a.download = filename;\n            document.body.appendChild(a);\n            a.click();\n            window.URL.revokeObjectURL(url);\n            document.body.removeChild(a);\n        } catch (error) {\n            console.error('Export failed:', error);\n            throw error;\n        }\n    }\n    // Export CSV endpoint\n    async exportProjectCSV(projectId) {\n        try {\n            const response = await fetch(`${API_BASE_URL}/projects/${projectId}/export-csv`);\n            if (!response.ok) {\n                throw new Error(`HTTP error! status: ${response.status}`);\n            }\n            // Get filename from Content-Disposition header or use default\n            const contentDisposition = response.headers.get('Content-Disposition');\n            let filename = 'classifications.csv';\n            if (contentDisposition) {\n                const filenameMatch = contentDisposition.match(/filename=\"(.+)\"/);\n                if (filenameMatch) {\n                    filename = filenameMatch[1];\n                }\n            }\n            // Create blob and download\n            const blob = await response.blob();\n            const url = window.URL.createObjectURL(blob);\n            const a = document.createElement('a');\n            a.href = url;\n            a.download = filename;\n            document.body.appendChild(a);\n            a.click();\n            window.URL.revokeObjectURL(url);\n            document.body.removeChild(a);\n        } catch (error) {\n            console.error('CSV export failed:', error);\n            throw error;\n        }\n    }\n    // Utility method to get image URL\n    getImageUrl(imagePath) {\n        return `${API_BASE_URL.replace('/api', '')}/uploads/${imagePath}`;\n    }\n}\nconst apiService = new ApiService();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/services/api.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fprojects%2F%5Bid%5D%2Fclassify%2Fpage&page=%2Fprojects%2F%5Bid%5D%2Fclassify%2Fpage&appPaths=%2Fprojects%2F%5Bid%5D%2Fclassify%2Fpage&pagePath=private-next-app-dir%2Fprojects%2F%5Bid%5D%2Fclassify%2Fpage.tsx&appDir=%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();