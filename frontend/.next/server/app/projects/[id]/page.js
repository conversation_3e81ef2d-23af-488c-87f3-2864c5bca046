/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/projects/[id]/page";
exports.ids = ["app/projects/[id]/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fprojects%2F%5Bid%5D%2Fpage&page=%2Fprojects%2F%5Bid%5D%2Fpage&appPaths=%2Fprojects%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fprojects%2F%5Bid%5D%2Fpage.tsx&appDir=%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fprojects%2F%5Bid%5D%2Fpage&page=%2Fprojects%2F%5Bid%5D%2Fpage&appPaths=%2Fprojects%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fprojects%2F%5Bid%5D%2Fpage.tsx&appDir=%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/projects/[id]/page.tsx */ \"(rsc)/./src/app/projects/[id]/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'projects',\n        {\n        children: [\n        '[id]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"/Users/<USER>/Downloads/classify-test/frontend/src/app/projects/[id]/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"/Users/<USER>/Downloads/classify-test/frontend/src/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Downloads/classify-test/frontend/src/app/projects/[id]/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/projects/[id]/page\",\n        pathname: \"/projects/[id]\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fprojects%2F%5Bid%5D%2Fpage&page=%2Fprojects%2F%5Bid%5D%2Fpage&appPaths=%2Fprojects%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fprojects%2F%5Bid%5D%2Fpage.tsx&appDir=%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fsrc%2Fcomponents%2FProjectDetail.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fsrc%2Fcomponents%2FProjectDetail.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ProjectDetail.tsx */ \"(rsc)/./src/components/ProjectDetail.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGa2FkZG8lMkZEb3dubG9hZHMlMkZjbGFzc2lmeS10ZXN0JTJGZnJvbnRlbmQlMkZzcmMlMkZjb21wb25lbnRzJTJGUHJvamVjdERldGFpbC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnTEFBZ0oiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCIvVXNlcnMva2FkZG8vRG93bmxvYWRzL2NsYXNzaWZ5LXRlc3QvZnJvbnRlbmQvc3JjL2NvbXBvbmVudHMvUHJvamVjdERldGFpbC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fsrc%2Fcomponents%2FProjectDetail.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIi9Vc2Vycy9rYWRkby9Eb3dubG9hZHMvY2xhc3NpZnktdGVzdC9mcm9udGVuZC9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"719cb0fc3f63\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvVXNlcnMva2FkZG8vRG93bmxvYWRzL2NsYXNzaWZ5LXRlc3QvZnJvbnRlbmQvc3JjL2FwcC9nbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjcxOWNiMGZjM2Y2M1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n\nconst metadata = {\n    title: \"Image Classification Tool\",\n    description: \"A flexible web application for manual classification of images\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default().variable)} antialiased bg-gray-50 min-h-screen`,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                    className: \"bg-white shadow-sm border-b\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center h-16\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl font-semibold text-gray-900\",\n                                    children: \"Image Classification Tool\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/app/layout.tsx\",\n                                    lineNumber: 34,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/app/layout.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/app/layout.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/app/layout.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/app/layout.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/app/layout.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/app/layout.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/app/layout.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/projects/[id]/page.tsx":
/*!****************************************!*\
  !*** ./src/app/projects/[id]/page.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProjectPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ProjectDetail__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ProjectDetail */ \"(rsc)/./src/components/ProjectDetail.tsx\");\n\n\nasync function ProjectPage({ params }) {\n    const { id } = await params;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProjectDetail__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        projectId: id\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/app/projects/[id]/page.tsx\",\n        lineNumber: 11,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL3Byb2plY3RzL1tpZF0vcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBdUQ7QUFReEMsZUFBZUMsWUFBWSxFQUFFQyxNQUFNLEVBQW9CO0lBQ3BFLE1BQU0sRUFBRUMsRUFBRSxFQUFFLEdBQUcsTUFBTUQ7SUFDckIscUJBQU8sOERBQUNGLGlFQUFhQTtRQUFDSSxXQUFXRDs7Ozs7O0FBQ25DIiwic291cmNlcyI6WyIvVXNlcnMva2FkZG8vRG93bmxvYWRzL2NsYXNzaWZ5LXRlc3QvZnJvbnRlbmQvc3JjL2FwcC9wcm9qZWN0cy9baWRdL3BhZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBQcm9qZWN0RGV0YWlsIGZyb20gJ0AvY29tcG9uZW50cy9Qcm9qZWN0RGV0YWlsJztcblxuaW50ZXJmYWNlIFByb2plY3RQYWdlUHJvcHMge1xuICBwYXJhbXM6IFByb21pc2U8e1xuICAgIGlkOiBzdHJpbmc7XG4gIH0+O1xufVxuXG5leHBvcnQgZGVmYXVsdCBhc3luYyBmdW5jdGlvbiBQcm9qZWN0UGFnZSh7IHBhcmFtcyB9OiBQcm9qZWN0UGFnZVByb3BzKSB7XG4gIGNvbnN0IHsgaWQgfSA9IGF3YWl0IHBhcmFtcztcbiAgcmV0dXJuIDxQcm9qZWN0RGV0YWlsIHByb2plY3RJZD17aWR9IC8+O1xufVxuIl0sIm5hbWVzIjpbIlByb2plY3REZXRhaWwiLCJQcm9qZWN0UGFnZSIsInBhcmFtcyIsImlkIiwicHJvamVjdElkIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/projects/[id]/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/ProjectDetail.tsx":
/*!******************************************!*\
  !*** ./src/components/ProjectDetail.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fsrc%2Fcomponents%2FProjectDetail.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fsrc%2Fcomponents%2FProjectDetail.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ProjectDetail.tsx */ \"(ssr)/./src/components/ProjectDetail.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGa2FkZG8lMkZEb3dubG9hZHMlMkZjbGFzc2lmeS10ZXN0JTJGZnJvbnRlbmQlMkZzcmMlMkZjb21wb25lbnRzJTJGUHJvamVjdERldGFpbC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnTEFBZ0oiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCIvVXNlcnMva2FkZG8vRG93bmxvYWRzL2NsYXNzaWZ5LXRlc3QvZnJvbnRlbmQvc3JjL2NvbXBvbmVudHMvUHJvamVjdERldGFpbC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fsrc%2Fcomponents%2FProjectDetail.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/ProjectDetail.tsx":
/*!******************************************!*\
  !*** ./src/components/ProjectDetail.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProjectDetail)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/api */ \"(ssr)/./src/services/api.ts\");\n/* harmony import */ var _UploadModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./UploadModal */ \"(ssr)/./src/components/UploadModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction ProjectDetail({ projectId }) {\n    const [project, setProject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [images, setImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showUploadModal, setShowUploadModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProjectDetail.useEffect\": ()=>{\n            loadProject();\n            loadImages();\n        }\n    }[\"ProjectDetail.useEffect\"], [\n        projectId\n    ]);\n    const loadProject = async ()=>{\n        try {\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_3__.apiService.getProject(projectId);\n            if (response.success) {\n                setProject(response.data);\n            } else {\n                setError(response.error || 'Failed to load project');\n            }\n        } catch (err) {\n            setError('Failed to load project');\n            console.error('Error loading project:', err);\n        }\n    };\n    const loadImages = async ()=>{\n        try {\n            setIsLoading(true);\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_3__.apiService.getProjectImagesWithClassifications(projectId);\n            if (response.success) {\n                setImages(response.data || []);\n            } else {\n                setError(response.error || 'Failed to load images');\n            }\n        } catch (err) {\n            setError('Failed to load images');\n            console.error('Error loading images:', err);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleUploadComplete = ()=>{\n        setShowUploadModal(false);\n        loadProject(); // Refresh project stats\n        loadImages(); // Refresh images\n    };\n    const handleExport = async ()=>{\n        if (!project) return;\n        try {\n            if (project.stats.classified_images === 0) {\n                alert('No classified images to export');\n                return;\n            }\n            await _services_api__WEBPACK_IMPORTED_MODULE_3__.apiService.exportProject(project.id);\n        } catch (error) {\n            console.error('Error exporting project:', error);\n            alert('Failed to export project');\n        }\n    };\n    if (isLoading && !project) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                lineNumber: 80,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n            lineNumber: 79,\n            columnNumber: 7\n        }, this);\n    }\n    if (error && !project) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg\",\n            children: error\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n            lineNumber: 87,\n            columnNumber: 7\n        }, this);\n    }\n    if (!project) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                    children: \"Project not found\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    href: \"/\",\n                    className: \"text-blue-600 hover:text-blue-700\",\n                    children: \"Back to Projects\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                    lineNumber: 97,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n            lineNumber: 95,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-start mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4 mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    className: \"text-blue-600 hover:text-blue-700\",\n                                    children: \"← Back to Projects\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                children: project.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 11\n                            }, this),\n                            project.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-4\",\n                                children: project.description\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Classes:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2\",\n                                        children: project.classes.map((className, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-block bg-blue-100 text-blue-800 text-sm px-3 py-1 rounded-full\",\n                                                children: className\n                                            }, index, false, {\n                                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowUploadModal(true),\n                                className: \"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors\",\n                                children: \"Upload Images\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleExport,\n                                disabled: project.stats.classified_images === 0,\n                                className: \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: \"Export Results\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: project.stats.total_images\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"Total Images\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-green-600\",\n                                children: project.stats.classified_images\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"Classified\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-orange-600\",\n                                children: project.stats.unclassified_images\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"Remaining\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-blue-600\",\n                                children: [\n                                    project.stats.progress_percentage,\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"Complete\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                lineNumber: 153,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between text-sm text-gray-600 mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Classification Progress\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    project.stats.progress_percentage,\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full bg-gray-200 rounded-full h-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-600 h-3 rounded-full transition-all duration-300\",\n                            style: {\n                                width: `${project.stats.progress_percentage}%`\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                lineNumber: 173,\n                columnNumber: 7\n            }, this),\n            project.stats.total_images > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex space-x-4 mb-8\",\n                children: [\n                    project.stats.unclassified_images > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: `/projects/${project.id}/classify`,\n                        className: \"bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-colors\",\n                        children: [\n                            \"Start Classifying (\",\n                            project.stats.unclassified_images,\n                            \" remaining)\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: `/projects/${project.id}/classify`,\n                        className: \"bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors\",\n                        children: \"Review Classifications\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                lineNumber: 188,\n                columnNumber: 9\n            }, this),\n            isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center py-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                    lineNumber: 209,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                lineNumber: 208,\n                columnNumber: 9\n            }, this) : images.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-12 bg-gray-50 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-400 text-6xl mb-4\",\n                        children: \"\\uD83D\\uDCF7\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                        children: \"No images uploaded\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-6\",\n                        children: \"Upload a ZIP file containing images to start classifying\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setShowUploadModal(true),\n                        className: \"bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-colors\",\n                        children: \"Upload Images\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                lineNumber: 212,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4\",\n                children: images.map((image)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative group\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"aspect-square bg-gray-200 rounded-lg overflow-hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: _services_api__WEBPACK_IMPORTED_MODULE_3__.apiService.getImageUrl(image.file_path),\n                                    alt: image.original_filename,\n                                    className: \"w-full h-full object-cover\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-opacity rounded-lg flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"opacity-0 group-hover:opacity-100 text-white text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm font-medium truncate px-2\",\n                                            children: image.original_filename\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 19\n                                        }, this),\n                                        image.classification && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs bg-green-600 px-2 py-1 rounded mt-1\",\n                                            children: image.classification.class_name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 15\n                            }, this),\n                            image.status === 'classified' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-2 right-2 bg-green-600 text-white text-xs px-2 py-1 rounded\",\n                                children: \"✓\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, image.id, true, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                lineNumber: 226,\n                columnNumber: 9\n            }, this),\n            showUploadModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UploadModal__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                projectId: project.id,\n                onClose: ()=>setShowUploadModal(false),\n                onUploadComplete: handleUploadComplete\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                lineNumber: 260,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n        lineNumber: 105,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ProjectDetail.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/UploadModal.tsx":
/*!****************************************!*\
  !*** ./src/components/UploadModal.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ UploadModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/api */ \"(ssr)/./src/services/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction UploadModal({ projectId, onClose, onUploadComplete }) {\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [uploadResult, setUploadResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [dragOver, setDragOver] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const handleFileSelect = (file)=>{\n        if (!file.name.toLowerCase().endsWith('.zip')) {\n            setError('Please select a ZIP file');\n            return;\n        }\n        if (file.size > 100 * 1024 * 1024) {\n            setError('File size must be less than 100MB');\n            return;\n        }\n        uploadFile(file);\n    };\n    const uploadFile = async (file)=>{\n        try {\n            setIsUploading(true);\n            setError(null);\n            setUploadResult(null);\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_2__.apiService.uploadZip(projectId, file);\n            if (response.success && response.data) {\n                setUploadResult(response.data);\n                if (response.data.processed_files > 0) {\n                    // Auto-close after successful upload\n                    setTimeout(()=>{\n                        onUploadComplete();\n                    }, 2000);\n                }\n            } else {\n                setError(response.error || 'Upload failed');\n            }\n        } catch (err) {\n            setError('Upload failed');\n            console.error('Upload error:', err);\n        } finally{\n            setIsUploading(false);\n        }\n    };\n    const handleDrop = (e)=>{\n        e.preventDefault();\n        setDragOver(false);\n        const files = Array.from(e.dataTransfer.files);\n        if (files.length > 0) {\n            handleFileSelect(files[0]);\n        }\n    };\n    const handleDragOver = (e)=>{\n        e.preventDefault();\n        setDragOver(true);\n    };\n    const handleDragLeave = (e)=>{\n        e.preventDefault();\n        setDragOver(false);\n    };\n    const handleFileInputChange = (e)=>{\n        const files = e.target.files;\n        if (files && files.length > 0) {\n            handleFileSelect(files[0]);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg p-6 max-w-md w-full mx-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-900\",\n                            children: \"Upload Images\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/UploadModal.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"text-gray-400 hover:text-gray-600\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-6 h-6\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M6 18L18 6M6 6l12 12\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/UploadModal.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/UploadModal.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/UploadModal.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/UploadModal.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 9\n                }, this),\n                !isUploading && !uploadResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `border-2 border-dashed rounded-lg p-8 text-center transition-colors ${dragOver ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'}`,\n                            onDrop: handleDrop,\n                            onDragOver: handleDragOver,\n                            onDragLeave: handleDragLeave,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-400 text-4xl mb-4\",\n                                    children: \"\\uD83D\\uDCC1\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/UploadModal.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-4\",\n                                    children: \"Drag and drop a ZIP file here, or click to select\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/UploadModal.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>fileInputRef.current?.click(),\n                                    className: \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors\",\n                                    children: \"Select ZIP File\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/UploadModal.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    ref: fileInputRef,\n                                    type: \"file\",\n                                    accept: \".zip\",\n                                    onChange: handleFileInputChange,\n                                    className: \"hidden\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/UploadModal.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/UploadModal.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 text-sm text-gray-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mb-2\",\n                                    children: \"Requirements:\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/UploadModal.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"list-disc list-inside space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"ZIP file containing images (JPG, PNG, GIF, BMP, WebP)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/UploadModal.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Maximum file size: 100MB\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/UploadModal.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Images will be extracted and processed automatically\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/UploadModal.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/UploadModal.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/UploadModal.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true),\n                isUploading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/UploadModal.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Uploading and processing images...\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/UploadModal.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/UploadModal.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 11\n                }, this),\n                uploadResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"py-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-green-600 text-4xl mb-2\",\n                                    children: \"✅\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/UploadModal.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                    children: \"Upload Complete!\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/UploadModal.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/UploadModal.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 rounded-lg p-4 space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Total files:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/UploadModal.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: uploadResult.total_files\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/UploadModal.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/UploadModal.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Processed:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/UploadModal.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium text-green-600\",\n                                            children: uploadResult.processed_files\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/UploadModal.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/UploadModal.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Skipped:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/UploadModal.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium text-orange-600\",\n                                            children: uploadResult.skipped_files\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/UploadModal.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/UploadModal.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 15\n                                }, this),\n                                uploadResult.errors.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-red-600 font-medium mb-2\",\n                                            children: \"Errors:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/UploadModal.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-red-600 space-y-1\",\n                                            children: uploadResult.errors.map((error, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: error\n                                                }, index, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/UploadModal.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/UploadModal.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/UploadModal.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/UploadModal.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onUploadComplete,\n                                className: \"bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors\",\n                                children: \"Continue\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/UploadModal.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/UploadModal.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/UploadModal.tsx\",\n                    lineNumber: 153,\n                    columnNumber: 11\n                }, this),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mt-4\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/UploadModal.tsx\",\n                    lineNumber: 196,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/UploadModal.tsx\",\n            lineNumber: 90,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/UploadModal.tsx\",\n        lineNumber: 89,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/UploadModal.tsx\n");

/***/ }),

/***/ "(ssr)/./src/services/api.ts":
/*!*****************************!*\
  !*** ./src/services/api.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiService: () => (/* binding */ apiService)\n/* harmony export */ });\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api';\nclass ApiService {\n    async request(endpoint, options = {}) {\n        const url = `${API_BASE_URL}${endpoint}`;\n        try {\n            const response = await fetch(url, {\n                headers: {\n                    'Content-Type': 'application/json',\n                    ...options.headers\n                },\n                ...options\n            });\n            if (!response.ok) {\n                throw new Error(`HTTP error! status: ${response.status}`);\n            }\n            return await response.json();\n        } catch (error) {\n            console.error('API request failed:', error);\n            throw error;\n        }\n    }\n    // Project endpoints\n    async getProjects() {\n        return this.request('/projects');\n    }\n    async getProject(id) {\n        return this.request(`/projects/${id}`);\n    }\n    async createProject(data) {\n        return this.request('/projects', {\n            method: 'POST',\n            body: JSON.stringify(data)\n        });\n    }\n    async updateProject(id, data) {\n        return this.request(`/projects/${id}`, {\n            method: 'PUT',\n            body: JSON.stringify(data)\n        });\n    }\n    async deleteProject(id) {\n        return this.request(`/projects/${id}`, {\n            method: 'DELETE'\n        });\n    }\n    // Upload endpoint\n    async uploadZip(projectId, file) {\n        const formData = new FormData();\n        formData.append('zipFile', file);\n        try {\n            const response = await fetch(`${API_BASE_URL}/projects/${projectId}/upload`, {\n                method: 'POST',\n                body: formData\n            });\n            if (!response.ok) {\n                throw new Error(`HTTP error! status: ${response.status}`);\n            }\n            return await response.json();\n        } catch (error) {\n            console.error('Upload failed:', error);\n            throw error;\n        }\n    }\n    // Image endpoints\n    async getProjectImages(projectId, status) {\n        const params = status ? `?status=${status}` : '';\n        return this.request(`/images/project/${projectId}${params}`);\n    }\n    async getProjectImagesWithClassifications(projectId) {\n        return this.request(`/images/project/${projectId}/with-classifications`);\n    }\n    async getNextUnclassifiedImage(projectId, currentImageId) {\n        const params = currentImageId ? `?currentImageId=${currentImageId}` : '';\n        return this.request(`/images/project/${projectId}/next-unclassified${params}`);\n    }\n    // Classification endpoints\n    async classifyImage(data) {\n        return this.request('/classifications', {\n            method: 'POST',\n            body: JSON.stringify(data)\n        });\n    }\n    async removeClassification(imageId) {\n        return this.request(`/classifications/image/${imageId}`, {\n            method: 'DELETE'\n        });\n    }\n    async getClassificationStats(projectId) {\n        return this.request(`/classifications/project/${projectId}/stats`);\n    }\n    // Export endpoint\n    async exportProject(projectId) {\n        try {\n            const response = await fetch(`${API_BASE_URL}/projects/${projectId}/export`);\n            if (!response.ok) {\n                throw new Error(`HTTP error! status: ${response.status}`);\n            }\n            // Get filename from Content-Disposition header or use default\n            const contentDisposition = response.headers.get('Content-Disposition');\n            let filename = 'export.zip';\n            if (contentDisposition) {\n                const filenameMatch = contentDisposition.match(/filename=\"(.+)\"/);\n                if (filenameMatch) {\n                    filename = filenameMatch[1];\n                }\n            }\n            // Create blob and download\n            const blob = await response.blob();\n            const url = window.URL.createObjectURL(blob);\n            const a = document.createElement('a');\n            a.href = url;\n            a.download = filename;\n            document.body.appendChild(a);\n            a.click();\n            window.URL.revokeObjectURL(url);\n            document.body.removeChild(a);\n        } catch (error) {\n            console.error('Export failed:', error);\n            throw error;\n        }\n    }\n    // Utility method to get image URL\n    getImageUrl(imagePath) {\n        return `${API_BASE_URL.replace('/api', '')}/uploads/${imagePath}`;\n    }\n}\nconst apiService = new ApiService();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/services/api.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fprojects%2F%5Bid%5D%2Fpage&page=%2Fprojects%2F%5Bid%5D%2Fpage&appPaths=%2Fprojects%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fprojects%2F%5Bid%5D%2Fpage.tsx&appDir=%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fkaddo%2FDownloads%2Fclassify-test%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();