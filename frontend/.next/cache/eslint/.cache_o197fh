[{"/Users/<USER>/Downloads/classify-test/frontend/src/app/layout.tsx": "1", "/Users/<USER>/Downloads/classify-test/frontend/src/app/page.tsx": "2", "/Users/<USER>/Downloads/classify-test/frontend/src/app/projects/[id]/classify/page.tsx": "3", "/Users/<USER>/Downloads/classify-test/frontend/src/app/projects/[id]/page.tsx": "4", "/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx": "5", "/Users/<USER>/Downloads/classify-test/frontend/src/components/CreateProjectModal.tsx": "6", "/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectCard.tsx": "7", "/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDashboard.tsx": "8", "/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx": "9", "/Users/<USER>/Downloads/classify-test/frontend/src/components/UploadModal.tsx": "10", "/Users/<USER>/Downloads/classify-test/frontend/src/services/api.ts": "11", "/Users/<USER>/Downloads/classify-test/frontend/src/types/index.ts": "12"}, {"size": 1299, "mtime": 1751020818000, "results": "13", "hashOfConfig": "14"}, {"size": 129, "mtime": 1751021099318, "results": "15", "hashOfConfig": "14"}, {"size": 314, "mtime": 1751022542746, "results": "16", "hashOfConfig": "14"}, {"size": 281, "mtime": 1751022555860, "results": "17", "hashOfConfig": "14"}, {"size": 14752, "mtime": 1751024472896, "results": "18", "hashOfConfig": "14"}, {"size": 6811, "mtime": 1751021088457, "results": "19", "hashOfConfig": "14"}, {"size": 7559, "mtime": 1751024402569, "results": "20", "hashOfConfig": "14"}, {"size": 3439, "mtime": 1751021035293, "results": "21", "hashOfConfig": "14"}, {"size": 11816, "mtime": 1751024416227, "results": "22", "hashOfConfig": "14"}, {"size": 6989, "mtime": 1751021667105, "results": "23", "hashOfConfig": "14"}, {"size": 6167, "mtime": 1751024432546, "results": "24", "hashOfConfig": "14"}, {"size": 1577, "mtime": 1751024450445, "results": "25", "hashOfConfig": "14"}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "16mtbqd", {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Downloads/classify-test/frontend/src/app/layout.tsx", [], [], "/Users/<USER>/Downloads/classify-test/frontend/src/app/page.tsx", [], [], "/Users/<USER>/Downloads/classify-test/frontend/src/app/projects/[id]/classify/page.tsx", [], [], "/Users/<USER>/Downloads/classify-test/frontend/src/app/projects/[id]/page.tsx", [], [], "/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx", ["62", "63", "64", "65"], [], "/Users/<USER>/Downloads/classify-test/frontend/src/components/CreateProjectModal.tsx", [], [], "/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectCard.tsx", [], [], "/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDashboard.tsx", [], [], "/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx", ["66", "67"], [], "/Users/<USER>/Downloads/classify-test/frontend/src/components/UploadModal.tsx", [], [], "/Users/<USER>/Downloads/classify-test/frontend/src/services/api.ts", [], [], "/Users/<USER>/Downloads/classify-test/frontend/src/types/index.ts", [], [], {"ruleId": "68", "severity": 1, "message": "69", "line": 25, "column": 6, "nodeType": "70", "endLine": 25, "endColumn": 17, "suggestions": "71"}, {"ruleId": "68", "severity": 1, "message": "72", "line": 225, "column": 6, "nodeType": "70", "endLine": 225, "endColumn": 55, "suggestions": "73"}, {"ruleId": "74", "severity": 1, "message": "75", "line": 327, "column": 17, "nodeType": "76", "endLine": 331, "endColumn": 19}, {"ruleId": "74", "severity": 1, "message": "75", "line": 417, "column": 19, "nodeType": "76", "endLine": 421, "endColumn": 21}, {"ruleId": "68", "severity": 1, "message": "69", "line": 23, "column": 6, "nodeType": "70", "endLine": 23, "endColumn": 17, "suggestions": "77"}, {"ruleId": "74", "severity": 1, "message": "75", "line": 256, "column": 19, "nodeType": "76", "endLine": 281, "endColumn": 21}, "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'loadImages' and 'loadProject'. Either include them or remove the dependency array.", "ArrayExpression", ["78"], "React Hook useEffect has missing dependencies: 'classifyImage', 'goToNext', 'goToPrevious', and 'removeClassification'. Either include them or remove the dependency array.", ["79"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", ["80"], {"desc": "81", "fix": "82"}, {"desc": "83", "fix": "84"}, {"desc": "81", "fix": "85"}, "Update the dependencies array to be: [loadImages, loadProject, projectId]", {"range": "86", "text": "87"}, "Update the dependencies array to be: [project, state.currentIndex, state.currentImage, classifyImage, goToPrevious, goToNext, removeClassification]", {"range": "88", "text": "89"}, {"range": "90", "text": "87"}, [668, 679], "[loadImages, loadProject, projectId]", [6622, 6671], "[project, state.currentIndex, state.currentImage, classifyImage, goToPrevious, goToNext, removeClassification]", [741, 752]]