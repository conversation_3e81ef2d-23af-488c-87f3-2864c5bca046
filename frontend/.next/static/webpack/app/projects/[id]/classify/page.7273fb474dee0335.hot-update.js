"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/classify/page",{

/***/ "(app-pages-browser)/./src/components/ClassificationInterface.tsx":
/*!****************************************************!*\
  !*** ./src/components/ClassificationInterface.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ClassificationInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/api */ \"(app-pages-browser)/./src/services/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction ClassificationInterface(param) {\n    let { projectId } = param;\n    _s();\n    const [project, setProject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        currentImage: null,\n        images: [],\n        currentIndex: 0,\n        isLoading: true,\n        error: null\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ClassificationInterface.useEffect\": ()=>{\n            loadProject();\n            loadImages();\n        }\n    }[\"ClassificationInterface.useEffect\"], [\n        projectId\n    ]);\n    const loadProject = async ()=>{\n        try {\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_3__.apiService.getProject(projectId);\n            if (response.success) {\n                setProject(response.data);\n            } else {\n                setState((prev)=>({\n                        ...prev,\n                        error: response.error || 'Failed to load project'\n                    }));\n            }\n        } catch (err) {\n            setState((prev)=>({\n                    ...prev,\n                    error: 'Failed to load project'\n                }));\n            console.error('Error loading project:', err);\n        }\n    };\n    const loadImages = async ()=>{\n        try {\n            setState((prev)=>({\n                    ...prev,\n                    isLoading: true,\n                    error: null\n                }));\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_3__.apiService.getProjectImagesWithClassifications(projectId);\n            if (response.success && response.data) {\n                const images = response.data;\n                setState((prev)=>({\n                        ...prev,\n                        images,\n                        currentImage: images.length > 0 ? images[0] : null,\n                        currentIndex: 0,\n                        isLoading: false\n                    }));\n            } else {\n                setState((prev)=>({\n                        ...prev,\n                        error: response.error || 'Failed to load images',\n                        isLoading: false\n                    }));\n            }\n        } catch (err) {\n            setState((prev)=>({\n                    ...prev,\n                    error: 'Failed to load images',\n                    isLoading: false\n                }));\n            console.error('Error loading images:', err);\n        }\n    };\n    const classifyImage = async (className)=>{\n        var _state_currentImage_classification;\n        if (!state.currentImage) return;\n        // Check if image is already classified with the same class\n        if (((_state_currentImage_classification = state.currentImage.classification) === null || _state_currentImage_classification === void 0 ? void 0 : _state_currentImage_classification.class_name) === className) {\n            return; // Already classified with this class, do nothing\n        }\n        try {\n            setState((prev)=>({\n                    ...prev,\n                    error: null\n                })); // Clear any previous errors\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_3__.apiService.classifyImage({\n                image_id: state.currentImage.id,\n                class_name: className\n            });\n            if (response.success) {\n                // Update the current image with classification\n                const updatedImage = {\n                    ...state.currentImage,\n                    status: 'classified',\n                    classification: {\n                        id: response.data.id,\n                        image_id: state.currentImage.id,\n                        project_id: projectId,\n                        class_name: className,\n                        classified_at: new Date().toISOString()\n                    }\n                };\n                // Update images array\n                const updatedImages = state.images.map((img)=>img.id === state.currentImage.id ? updatedImage : img);\n                setState((prev)=>({\n                        ...prev,\n                        images: updatedImages,\n                        currentImage: updatedImage,\n                        error: null\n                    }));\n                // Auto-advance to next unclassified image after a short delay\n                setTimeout(()=>{\n                    goToNextUnclassified();\n                }, 300);\n                // Refresh project stats\n                loadProject();\n            } else {\n                setState((prev)=>({\n                        ...prev,\n                        error: response.error || 'Failed to classify image'\n                    }));\n            }\n        } catch (err) {\n            setState((prev)=>({\n                    ...prev,\n                    error: 'Failed to classify image'\n                }));\n            console.error('Error classifying image:', err);\n        }\n    };\n    const removeClassification = async ()=>{\n        if (!state.currentImage || !state.currentImage.classification) return;\n        try {\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_3__.apiService.removeClassification(state.currentImage.id);\n            if (response.success) {\n                // Update the current image to remove classification\n                const updatedImage = {\n                    ...state.currentImage,\n                    status: 'unclassified',\n                    classification: undefined\n                };\n                // Update images array\n                const updatedImages = state.images.map((img)=>img.id === state.currentImage.id ? updatedImage : img);\n                setState((prev)=>({\n                        ...prev,\n                        images: updatedImages,\n                        currentImage: updatedImage\n                    }));\n                // Refresh project stats\n                loadProject();\n            } else {\n                setState((prev)=>({\n                        ...prev,\n                        error: response.error || 'Failed to remove classification'\n                    }));\n            }\n        } catch (err) {\n            setState((prev)=>({\n                    ...prev,\n                    error: 'Failed to remove classification'\n                }));\n            console.error('Error removing classification:', err);\n        }\n    };\n    const goToImage = (index)=>{\n        if (index >= 0 && index < state.images.length) {\n            setState((prev)=>({\n                    ...prev,\n                    currentIndex: index,\n                    currentImage: prev.images[index]\n                }));\n        }\n    };\n    const goToNextUnclassified = ()=>{\n        const nextUnclassified = state.images.findIndex((img, index)=>index > state.currentIndex && img.status === 'unclassified');\n        if (nextUnclassified !== -1) {\n            goToImage(nextUnclassified);\n        } else {\n            // Look from the beginning\n            const firstUnclassified = state.images.findIndex((img)=>img.status === 'unclassified');\n            if (firstUnclassified !== -1) {\n                goToImage(firstUnclassified);\n            }\n        }\n    };\n    const goToPrevious = ()=>{\n        if (state.currentIndex > 0) {\n            goToImage(state.currentIndex - 1);\n        }\n    };\n    const goToNext = ()=>{\n        if (state.currentIndex < state.images.length - 1) {\n            goToImage(state.currentIndex + 1);\n        }\n    };\n    // Keyboard navigation\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ClassificationInterface.useEffect\": ()=>{\n            const handleKeyPress = {\n                \"ClassificationInterface.useEffect.handleKeyPress\": (e)=>{\n                    if (!project) return;\n                    if (e.key >= '1' && e.key <= '9') {\n                        const classIndex = parseInt(e.key) - 1;\n                        if (classIndex < project.classes.length) {\n                            classifyImage(project.classes[classIndex]);\n                        }\n                    } else if (e.key === 'ArrowLeft') {\n                        goToPrevious();\n                    } else if (e.key === 'ArrowRight') {\n                        goToNext();\n                    } else if (e.key === 'Backspace' || e.key === 'Delete') {\n                        removeClassification();\n                    }\n                }\n            }[\"ClassificationInterface.useEffect.handleKeyPress\"];\n            window.addEventListener('keydown', handleKeyPress);\n            return ({\n                \"ClassificationInterface.useEffect\": ()=>window.removeEventListener('keydown', handleKeyPress)\n            })[\"ClassificationInterface.useEffect\"];\n        }\n    }[\"ClassificationInterface.useEffect\"], [\n        project,\n        state.currentIndex,\n        state.currentImage\n    ]);\n    if (state.isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                lineNumber: 230,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n            lineNumber: 229,\n            columnNumber: 7\n        }, this);\n    }\n    if (state.error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg\",\n            children: state.error\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n            lineNumber: 237,\n            columnNumber: 7\n        }, this);\n    }\n    if (!project) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                    children: \"Project not found\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                    lineNumber: 246,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    href: \"/\",\n                    className: \"text-blue-600 hover:text-blue-700\",\n                    children: \"Back to Projects\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                    lineNumber: 247,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n            lineNumber: 245,\n            columnNumber: 7\n        }, this);\n    }\n    if (state.images.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-gray-400 text-6xl mb-4\",\n                    children: \"\\uD83D\\uDCF7\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                    lineNumber: 257,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                    children: \"No images to classify\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                    lineNumber: 258,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600 mb-6\",\n                    children: \"Upload images to this project to start classifying\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                    lineNumber: 259,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    href: \"/projects/\".concat(projectId),\n                    className: \"bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors\",\n                    children: \"Back to Project\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                    lineNumber: 262,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n            lineNumber: 256,\n            columnNumber: 7\n        }, this);\n    }\n    const unclassifiedCount = state.images.filter((img)=>img.status === 'unclassified').length;\n    const progressPercentage = Math.round((state.images.length - unclassifiedCount) / state.images.length * 100);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-6xl mx-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/projects/\".concat(projectId),\n                                className: \"text-blue-600 hover:text-blue-700 mb-2 inline-block\",\n                                children: [\n                                    \"← Back to \",\n                                    project.name\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                lineNumber: 280,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"Classification Interface\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                        lineNumber: 279,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-right\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: [\n                                    \"Image \",\n                                    state.currentIndex + 1,\n                                    \" of \",\n                                    state.images.length\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: [\n                                    unclassifiedCount,\n                                    \" remaining • \",\n                                    progressPercentage,\n                                    \"% complete\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                lineNumber: 278,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full bg-gray-200 rounded-full h-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-green-600 h-2 rounded-full transition-all duration-300\",\n                        style: {\n                            width: \"\".concat(progressPercentage, \"%\")\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                        lineNumber: 301,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                    lineNumber: 300,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                lineNumber: 299,\n                columnNumber: 7\n            }, this),\n            state.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6\",\n                children: [\n                    state.error,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setState((prev)=>({\n                                    ...prev,\n                                    error: null\n                                })),\n                        className: \"ml-2 text-red-500 hover:text-red-700\",\n                        children: \"\\xd7\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                        lineNumber: 312,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                lineNumber: 310,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2\",\n                        children: state.currentImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"aspect-square bg-gray-100 rounded-lg overflow-hidden mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: _services_api__WEBPACK_IMPORTED_MODULE_3__.apiService.getImageUrl(state.currentImage.file_path),\n                                        alt: state.currentImage.original_filename,\n                                        className: \"w-full h-full object-contain\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium text-gray-900\",\n                                                children: state.currentImage.original_filename\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                                lineNumber: 336,\n                                                columnNumber: 19\n                                            }, this),\n                                            state.currentImage.classification && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-green-600 mt-1\",\n                                                children: [\n                                                    \"Classified as: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: state.currentImage.classification.class_name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 38\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                    lineNumber: 334,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: goToPrevious,\n                                            disabled: state.currentIndex === 0,\n                                            className: \"bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n                                            children: \"← Previous\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: goToNextUnclassified,\n                                            className: \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors\",\n                                            children: \"Next Unclassified\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: goToNext,\n                                            disabled: state.currentIndex === state.images.length - 1,\n                                            className: \"bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n                                            children: \"Next →\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                    lineNumber: 347,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                            lineNumber: 325,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                        lineNumber: 323,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                        children: \"Classify Image\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                        lineNumber: 379,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: project.classes.map((className, index)=>{\n                                            var _state_currentImage_classification, _state_currentImage;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>classifyImage(className),\n                                                className: \"w-full text-left px-4 py-3 rounded-lg font-medium transition-colors \".concat(((_state_currentImage = state.currentImage) === null || _state_currentImage === void 0 ? void 0 : (_state_currentImage_classification = _state_currentImage.classification) === null || _state_currentImage_classification === void 0 ? void 0 : _state_currentImage_classification.class_name) === className ? 'bg-green-600 text-white' : 'bg-gray-100 hover:bg-gray-200 text-gray-900'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-500 mr-2\",\n                                                        children: [\n                                                            \"(\",\n                                                            index + 1,\n                                                            \")\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                                        lineNumber: 391,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    className\n                                                ]\n                                            }, className, true, {\n                                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 17\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 text-xs text-gray-500\",\n                                        children: [\n                                            \"Use keyboard shortcuts: 1-\",\n                                            project.classes.length,\n                                            \" to classify, ← → to navigate, Backspace to remove\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                        lineNumber: 396,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                lineNumber: 378,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                        children: \"All Images\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-4 gap-2 max-h-96 overflow-y-auto\",\n                                        children: state.images.map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>goToImage(index),\n                                                className: \"relative aspect-square rounded overflow-hidden border-2 transition-colors \".concat(index === state.currentIndex ? 'border-blue-500' : image.status === 'classified' ? 'border-green-500' : 'border-gray-200'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: _services_api__WEBPACK_IMPORTED_MODULE_3__.apiService.getImageUrl(image.file_path),\n                                                        alt: image.original_filename,\n                                                        className: \"w-full h-full object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                                        lineNumber: 417,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    image.status === 'classified' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute top-1 right-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-green-600 text-white text-xs px-1 py-0.5 rounded\",\n                                                            children: \"✓\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                                            lineNumber: 424,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                                        lineNumber: 423,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, image.id, true, {\n                                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                lineNumber: 402,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                        lineNumber: 376,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                lineNumber: 321,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n        lineNumber: 276,\n        columnNumber: 5\n    }, this);\n}\n_s(ClassificationInterface, \"npNFaO6OZWMB5oic/r0BblPesTs=\");\n_c = ClassificationInterface;\nvar _c;\n$RefreshReg$(_c, \"ClassificationInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ClassificationInterface.tsx\n"));

/***/ })

});