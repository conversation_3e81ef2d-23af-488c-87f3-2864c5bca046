"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/classify/page",{

/***/ "(app-pages-browser)/./src/components/ClassificationInterface.tsx":
/*!****************************************************!*\
  !*** ./src/components/ClassificationInterface.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ClassificationInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/api */ \"(app-pages-browser)/./src/services/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction ClassificationInterface(param) {\n    let { projectId } = param;\n    _s();\n    const [project, setProject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        currentImage: null,\n        images: [],\n        currentIndex: 0,\n        isLoading: true,\n        error: null\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ClassificationInterface.useEffect\": ()=>{\n            loadProject();\n            loadImages();\n        }\n    }[\"ClassificationInterface.useEffect\"], [\n        projectId\n    ]);\n    const loadProject = async ()=>{\n        try {\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_3__.apiService.getProject(projectId);\n            if (response.success) {\n                setProject(response.data);\n            } else {\n                setState((prev)=>({\n                        ...prev,\n                        error: response.error || 'Failed to load project'\n                    }));\n            }\n        } catch (err) {\n            setState((prev)=>({\n                    ...prev,\n                    error: 'Failed to load project'\n                }));\n            console.error('Error loading project:', err);\n        }\n    };\n    const loadImages = async ()=>{\n        try {\n            setState((prev)=>({\n                    ...prev,\n                    isLoading: true,\n                    error: null\n                }));\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_3__.apiService.getProjectImagesWithClassifications(projectId);\n            if (response.success && response.data) {\n                const images = response.data;\n                setState((prev)=>({\n                        ...prev,\n                        images,\n                        currentImage: images.length > 0 ? images[0] : null,\n                        currentIndex: 0,\n                        isLoading: false\n                    }));\n            } else {\n                setState((prev)=>({\n                        ...prev,\n                        error: response.error || 'Failed to load images',\n                        isLoading: false\n                    }));\n            }\n        } catch (err) {\n            setState((prev)=>({\n                    ...prev,\n                    error: 'Failed to load images',\n                    isLoading: false\n                }));\n            console.error('Error loading images:', err);\n        }\n    };\n    const classifyImage = async (className)=>{\n        var _state_currentImage_classification;\n        if (!state.currentImage) return;\n        // Check if image is already classified with the same class\n        if (((_state_currentImage_classification = state.currentImage.classification) === null || _state_currentImage_classification === void 0 ? void 0 : _state_currentImage_classification.class_name) === className) {\n            return; // Already classified with this class, do nothing\n        }\n        try {\n            setState((prev)=>({\n                    ...prev,\n                    error: null\n                })); // Clear any previous errors\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_3__.apiService.classifyImage({\n                image_id: state.currentImage.id,\n                class_name: className\n            });\n            if (response.success) {\n                // Update the current image with classification\n                const updatedImage = {\n                    ...state.currentImage,\n                    status: 'classified',\n                    classification: {\n                        id: response.data.id,\n                        image_id: state.currentImage.id,\n                        project_id: projectId,\n                        class_name: className,\n                        classified_at: new Date().toISOString()\n                    }\n                };\n                // Update images array\n                const updatedImages = state.images.map((img)=>img.id === state.currentImage.id ? updatedImage : img);\n                setState((prev)=>({\n                        ...prev,\n                        images: updatedImages,\n                        currentImage: updatedImage,\n                        error: null\n                    }));\n                // Auto-advance to next unclassified image after a short delay\n                setTimeout(()=>{\n                    goToNextUnclassified();\n                }, 300);\n                // Refresh project stats\n                loadProject();\n            } else {\n                setState((prev)=>({\n                        ...prev,\n                        error: response.error || 'Failed to classify image'\n                    }));\n            }\n        } catch (err) {\n            setState((prev)=>({\n                    ...prev,\n                    error: 'Failed to classify image'\n                }));\n            console.error('Error classifying image:', err);\n        }\n    };\n    const removeClassification = async ()=>{\n        if (!state.currentImage || !state.currentImage.classification) return;\n        try {\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_3__.apiService.removeClassification(state.currentImage.id);\n            if (response.success) {\n                // Update the current image to remove classification\n                const updatedImage = {\n                    ...state.currentImage,\n                    status: 'unclassified',\n                    classification: undefined\n                };\n                // Update images array\n                const updatedImages = state.images.map((img)=>img.id === state.currentImage.id ? updatedImage : img);\n                setState((prev)=>({\n                        ...prev,\n                        images: updatedImages,\n                        currentImage: updatedImage\n                    }));\n                // Refresh project stats\n                loadProject();\n            } else {\n                setState((prev)=>({\n                        ...prev,\n                        error: response.error || 'Failed to remove classification'\n                    }));\n            }\n        } catch (err) {\n            setState((prev)=>({\n                    ...prev,\n                    error: 'Failed to remove classification'\n                }));\n            console.error('Error removing classification:', err);\n        }\n    };\n    const goToImage = (index)=>{\n        if (index >= 0 && index < state.images.length) {\n            setState((prev)=>({\n                    ...prev,\n                    currentIndex: index,\n                    currentImage: prev.images[index]\n                }));\n        }\n    };\n    const goToNextUnclassified = ()=>{\n        const nextUnclassified = state.images.findIndex((img, index)=>index > state.currentIndex && img.status === 'unclassified');\n        if (nextUnclassified !== -1) {\n            goToImage(nextUnclassified);\n        } else {\n            // Look from the beginning\n            const firstUnclassified = state.images.findIndex((img)=>img.status === 'unclassified');\n            if (firstUnclassified !== -1) {\n                goToImage(firstUnclassified);\n            }\n        }\n    };\n    const goToPrevious = ()=>{\n        if (state.currentIndex > 0) {\n            goToImage(state.currentIndex - 1);\n        }\n    };\n    const goToNext = ()=>{\n        if (state.currentIndex < state.images.length - 1) {\n            goToImage(state.currentIndex + 1);\n        }\n    };\n    // Keyboard navigation\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ClassificationInterface.useEffect\": ()=>{\n            const handleKeyPress = {\n                \"ClassificationInterface.useEffect.handleKeyPress\": (e)=>{\n                    if (!project) return;\n                    if (e.key >= '1' && e.key <= '9') {\n                        const classIndex = parseInt(e.key) - 1;\n                        if (classIndex < project.classes.length) {\n                            classifyImage(project.classes[classIndex]);\n                        }\n                    } else if (e.key === 'ArrowLeft') {\n                        goToPrevious();\n                    } else if (e.key === 'ArrowRight') {\n                        goToNext();\n                    } else if (e.key === 'Backspace' || e.key === 'Delete') {\n                        removeClassification();\n                    }\n                }\n            }[\"ClassificationInterface.useEffect.handleKeyPress\"];\n            window.addEventListener('keydown', handleKeyPress);\n            return ({\n                \"ClassificationInterface.useEffect\": ()=>window.removeEventListener('keydown', handleKeyPress)\n            })[\"ClassificationInterface.useEffect\"];\n        }\n    }[\"ClassificationInterface.useEffect\"], [\n        project,\n        state.currentIndex,\n        state.currentImage\n    ]);\n    if (state.isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                lineNumber: 230,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n            lineNumber: 229,\n            columnNumber: 7\n        }, this);\n    }\n    if (state.error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg\",\n            children: state.error\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n            lineNumber: 237,\n            columnNumber: 7\n        }, this);\n    }\n    if (!project) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                    children: \"Project not found\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                    lineNumber: 246,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    href: \"/\",\n                    className: \"text-blue-600 hover:text-blue-700\",\n                    children: \"Back to Projects\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                    lineNumber: 247,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n            lineNumber: 245,\n            columnNumber: 7\n        }, this);\n    }\n    if (state.images.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-gray-400 text-6xl mb-4\",\n                    children: \"\\uD83D\\uDCF7\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                    lineNumber: 257,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                    children: \"No images to classify\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                    lineNumber: 258,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600 mb-6\",\n                    children: \"Upload images to this project to start classifying\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                    lineNumber: 259,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    href: \"/projects/\".concat(projectId),\n                    className: \"bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors\",\n                    children: \"Back to Project\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                    lineNumber: 262,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n            lineNumber: 256,\n            columnNumber: 7\n        }, this);\n    }\n    const unclassifiedCount = state.images.filter((img)=>img.status === 'unclassified').length;\n    const progressPercentage = Math.round((state.images.length - unclassifiedCount) / state.images.length * 100);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-6xl mx-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/projects/\".concat(projectId),\n                                className: \"text-blue-600 hover:text-blue-700 mb-2 inline-block\",\n                                children: [\n                                    \"← Back to \",\n                                    project.name\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                lineNumber: 280,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"Classification Interface\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                        lineNumber: 279,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-right\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: [\n                                    \"Image \",\n                                    state.currentIndex + 1,\n                                    \" of \",\n                                    state.images.length\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: [\n                                    unclassifiedCount,\n                                    \" remaining • \",\n                                    progressPercentage,\n                                    \"% complete\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                lineNumber: 278,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full bg-gray-200 rounded-full h-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-green-600 h-2 rounded-full transition-all duration-300\",\n                        style: {\n                            width: \"\".concat(progressPercentage, \"%\")\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                        lineNumber: 301,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                    lineNumber: 300,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                lineNumber: 299,\n                columnNumber: 7\n            }, this),\n            state.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6\",\n                children: [\n                    state.error,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setState((prev)=>({\n                                    ...prev,\n                                    error: null\n                                })),\n                        className: \"ml-2 text-red-500 hover:text-red-700\",\n                        children: \"\\xd7\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                        lineNumber: 312,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                lineNumber: 310,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2\",\n                        children: state.currentImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"aspect-square bg-gray-100 rounded-lg overflow-hidden mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: _services_api__WEBPACK_IMPORTED_MODULE_3__.apiService.getImageUrl(state.currentImage.file_path),\n                                        alt: state.currentImage.original_filename,\n                                        className: \"w-full h-full object-contain\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-medium text-gray-900\",\n                                                    children: state.currentImage.original_filename\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                                    lineNumber: 336,\n                                                    columnNumber: 19\n                                                }, this),\n                                                state.currentImage.classification && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-green-600 mt-1\",\n                                                    children: [\n                                                        \"Classified as: \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: state.currentImage.classification.class_name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                                            lineNumber: 339,\n                                                            columnNumber: 38\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 17\n                                        }, this),\n                                        state.currentImage.classification && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: removeClassification,\n                                            className: \"text-red-600 hover:text-red-700 text-sm\",\n                                            children: \"Remove Classification\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                            lineNumber: 344,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                    lineNumber: 334,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: goToPrevious,\n                                            disabled: state.currentIndex === 0,\n                                            className: \"bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n                                            children: \"← Previous\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                            lineNumber: 355,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: goToNextUnclassified,\n                                            className: \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors\",\n                                            children: \"Next Unclassified\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: goToNext,\n                                            disabled: state.currentIndex === state.images.length - 1,\n                                            className: \"bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n                                            children: \"Next →\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                    lineNumber: 354,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                            lineNumber: 325,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                        lineNumber: 323,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                        children: \"Classify Image\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                        lineNumber: 386,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: project.classes.map((className, index)=>{\n                                            var _state_currentImage_classification, _state_currentImage;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>classifyImage(className),\n                                                className: \"w-full text-left px-4 py-3 rounded-lg font-medium transition-colors \".concat(((_state_currentImage = state.currentImage) === null || _state_currentImage === void 0 ? void 0 : (_state_currentImage_classification = _state_currentImage.classification) === null || _state_currentImage_classification === void 0 ? void 0 : _state_currentImage_classification.class_name) === className ? 'bg-green-600 text-white' : 'bg-gray-100 hover:bg-gray-200 text-gray-900'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-500 mr-2\",\n                                                        children: [\n                                                            \"(\",\n                                                            index + 1,\n                                                            \")\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                                        lineNumber: 398,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    className\n                                                ]\n                                            }, className, true, {\n                                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                                lineNumber: 389,\n                                                columnNumber: 17\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                        lineNumber: 387,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 text-xs text-gray-500\",\n                                        children: [\n                                            \"Use keyboard shortcuts: 1-\",\n                                            project.classes.length,\n                                            \" to classify, ← → to navigate, Backspace to remove\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                lineNumber: 385,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                        children: \"All Images\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-4 gap-2 max-h-96 overflow-y-auto\",\n                                        children: state.images.map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>goToImage(index),\n                                                className: \"relative aspect-square rounded overflow-hidden border-2 transition-colors \".concat(index === state.currentIndex ? 'border-blue-500' : image.status === 'classified' ? 'border-green-500' : 'border-gray-200'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: _services_api__WEBPACK_IMPORTED_MODULE_3__.apiService.getImageUrl(image.file_path),\n                                                        alt: image.original_filename,\n                                                        className: \"w-full h-full object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                                        lineNumber: 424,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    image.status === 'classified' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute top-1 right-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-green-600 text-white text-xs px-1 py-0.5 rounded\",\n                                                            children: \"✓\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                                            lineNumber: 431,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                                        lineNumber: 430,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, image.id, true, {\n                                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                                lineNumber: 413,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                        lineNumber: 411,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                                lineNumber: 409,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                        lineNumber: 383,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n                lineNumber: 321,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ClassificationInterface.tsx\",\n        lineNumber: 276,\n        columnNumber: 5\n    }, this);\n}\n_s(ClassificationInterface, \"npNFaO6OZWMB5oic/r0BblPesTs=\");\n_c = ClassificationInterface;\nvar _c;\n$RefreshReg$(_c, \"ClassificationInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL0NsYXNzaWZpY2F0aW9uSW50ZXJmYWNlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFFNEM7QUFDZjtBQUVlO0FBTTdCLFNBQVNJLHdCQUF3QixLQUEyQztRQUEzQyxFQUFFQyxTQUFTLEVBQWdDLEdBQTNDOztJQUM5QyxNQUFNLENBQUNDLFNBQVNDLFdBQVcsR0FBR1AsK0NBQVFBLENBQWlCO0lBQ3ZELE1BQU0sQ0FBQ1EsT0FBT0MsU0FBUyxHQUFHVCwrQ0FBUUEsQ0FBc0I7UUFDdERVLGNBQWM7UUFDZEMsUUFBUSxFQUFFO1FBQ1ZDLGNBQWM7UUFDZEMsV0FBVztRQUNYQyxPQUFPO0lBQ1Q7SUFFQWIsZ0RBQVNBOzZDQUFDO1lBQ1JjO1lBQ0FDO1FBQ0Y7NENBQUc7UUFBQ1g7S0FBVTtJQUVkLE1BQU1VLGNBQWM7UUFDbEIsSUFBSTtZQUNGLE1BQU1FLFdBQVcsTUFBTWQscURBQVVBLENBQUNlLFVBQVUsQ0FBQ2I7WUFDN0MsSUFBSVksU0FBU0UsT0FBTyxFQUFFO2dCQUNwQlosV0FBV1UsU0FBU0csSUFBSTtZQUMxQixPQUFPO2dCQUNMWCxTQUFTWSxDQUFBQSxPQUFTO3dCQUFFLEdBQUdBLElBQUk7d0JBQUVQLE9BQU9HLFNBQVNILEtBQUssSUFBSTtvQkFBeUI7WUFDakY7UUFDRixFQUFFLE9BQU9RLEtBQUs7WUFDWmIsU0FBU1ksQ0FBQUEsT0FBUztvQkFBRSxHQUFHQSxJQUFJO29CQUFFUCxPQUFPO2dCQUF5QjtZQUM3RFMsUUFBUVQsS0FBSyxDQUFDLDBCQUEwQlE7UUFDMUM7SUFDRjtJQUVBLE1BQU1OLGFBQWE7UUFDakIsSUFBSTtZQUNGUCxTQUFTWSxDQUFBQSxPQUFTO29CQUFFLEdBQUdBLElBQUk7b0JBQUVSLFdBQVc7b0JBQU1DLE9BQU87Z0JBQUs7WUFDMUQsTUFBTUcsV0FBVyxNQUFNZCxxREFBVUEsQ0FBQ3FCLG1DQUFtQyxDQUFDbkI7WUFFdEUsSUFBSVksU0FBU0UsT0FBTyxJQUFJRixTQUFTRyxJQUFJLEVBQUU7Z0JBQ3JDLE1BQU1ULFNBQVNNLFNBQVNHLElBQUk7Z0JBQzVCWCxTQUFTWSxDQUFBQSxPQUFTO3dCQUNoQixHQUFHQSxJQUFJO3dCQUNQVjt3QkFDQUQsY0FBY0MsT0FBT2MsTUFBTSxHQUFHLElBQUlkLE1BQU0sQ0FBQyxFQUFFLEdBQUc7d0JBQzlDQyxjQUFjO3dCQUNkQyxXQUFXO29CQUNiO1lBQ0YsT0FBTztnQkFDTEosU0FBU1ksQ0FBQUEsT0FBUzt3QkFDaEIsR0FBR0EsSUFBSTt3QkFDUFAsT0FBT0csU0FBU0gsS0FBSyxJQUFJO3dCQUN6QkQsV0FBVztvQkFDYjtZQUNGO1FBQ0YsRUFBRSxPQUFPUyxLQUFLO1lBQ1piLFNBQVNZLENBQUFBLE9BQVM7b0JBQ2hCLEdBQUdBLElBQUk7b0JBQ1BQLE9BQU87b0JBQ1BELFdBQVc7Z0JBQ2I7WUFDQVUsUUFBUVQsS0FBSyxDQUFDLHlCQUF5QlE7UUFDekM7SUFDRjtJQUVBLE1BQU1JLGdCQUFnQixPQUFPQztZQUl2Qm5CO1FBSEosSUFBSSxDQUFDQSxNQUFNRSxZQUFZLEVBQUU7UUFFekIsMkRBQTJEO1FBQzNELElBQUlGLEVBQUFBLHFDQUFBQSxNQUFNRSxZQUFZLENBQUNrQixjQUFjLGNBQWpDcEIseURBQUFBLG1DQUFtQ3FCLFVBQVUsTUFBS0YsV0FBVztZQUMvRCxRQUFRLGlEQUFpRDtRQUMzRDtRQUVBLElBQUk7WUFDRmxCLFNBQVNZLENBQUFBLE9BQVM7b0JBQUUsR0FBR0EsSUFBSTtvQkFBRVAsT0FBTztnQkFBSyxLQUFLLDRCQUE0QjtZQUUxRSxNQUFNRyxXQUFXLE1BQU1kLHFEQUFVQSxDQUFDdUIsYUFBYSxDQUFDO2dCQUM5Q0ksVUFBVXRCLE1BQU1FLFlBQVksQ0FBQ3FCLEVBQUU7Z0JBQy9CRixZQUFZRjtZQUNkO1lBRUEsSUFBSVYsU0FBU0UsT0FBTyxFQUFFO2dCQUNwQiwrQ0FBK0M7Z0JBQy9DLE1BQU1hLGVBQXdDO29CQUM1QyxHQUFHeEIsTUFBTUUsWUFBWTtvQkFDckJ1QixRQUFRO29CQUNSTCxnQkFBZ0I7d0JBQ2RHLElBQUlkLFNBQVNHLElBQUksQ0FBQ1csRUFBRTt3QkFDcEJELFVBQVV0QixNQUFNRSxZQUFZLENBQUNxQixFQUFFO3dCQUMvQkcsWUFBWTdCO3dCQUNad0IsWUFBWUY7d0JBQ1pRLGVBQWUsSUFBSUMsT0FBT0MsV0FBVztvQkFDdkM7Z0JBQ0Y7Z0JBRUEsc0JBQXNCO2dCQUN0QixNQUFNQyxnQkFBZ0I5QixNQUFNRyxNQUFNLENBQUM0QixHQUFHLENBQUNDLENBQUFBLE1BQ3JDQSxJQUFJVCxFQUFFLEtBQUt2QixNQUFNRSxZQUFZLENBQUVxQixFQUFFLEdBQUdDLGVBQWVRO2dCQUdyRC9CLFNBQVNZLENBQUFBLE9BQVM7d0JBQ2hCLEdBQUdBLElBQUk7d0JBQ1BWLFFBQVEyQjt3QkFDUjVCLGNBQWNzQjt3QkFDZGxCLE9BQU87b0JBQ1Q7Z0JBRUEsOERBQThEO2dCQUM5RDJCLFdBQVc7b0JBQ1RDO2dCQUNGLEdBQUc7Z0JBRUgsd0JBQXdCO2dCQUN4QjNCO1lBQ0YsT0FBTztnQkFDTE4sU0FBU1ksQ0FBQUEsT0FBUzt3QkFBRSxHQUFHQSxJQUFJO3dCQUFFUCxPQUFPRyxTQUFTSCxLQUFLLElBQUk7b0JBQTJCO1lBQ25GO1FBQ0YsRUFBRSxPQUFPUSxLQUFLO1lBQ1piLFNBQVNZLENBQUFBLE9BQVM7b0JBQUUsR0FBR0EsSUFBSTtvQkFBRVAsT0FBTztnQkFBMkI7WUFDL0RTLFFBQVFULEtBQUssQ0FBQyw0QkFBNEJRO1FBQzVDO0lBQ0Y7SUFFQSxNQUFNcUIsdUJBQXVCO1FBQzNCLElBQUksQ0FBQ25DLE1BQU1FLFlBQVksSUFBSSxDQUFDRixNQUFNRSxZQUFZLENBQUNrQixjQUFjLEVBQUU7UUFFL0QsSUFBSTtZQUNGLE1BQU1YLFdBQVcsTUFBTWQscURBQVVBLENBQUN3QyxvQkFBb0IsQ0FBQ25DLE1BQU1FLFlBQVksQ0FBQ3FCLEVBQUU7WUFFNUUsSUFBSWQsU0FBU0UsT0FBTyxFQUFFO2dCQUNwQixvREFBb0Q7Z0JBQ3BELE1BQU1hLGVBQXdDO29CQUM1QyxHQUFHeEIsTUFBTUUsWUFBWTtvQkFDckJ1QixRQUFRO29CQUNSTCxnQkFBZ0JnQjtnQkFDbEI7Z0JBRUEsc0JBQXNCO2dCQUN0QixNQUFNTixnQkFBZ0I5QixNQUFNRyxNQUFNLENBQUM0QixHQUFHLENBQUNDLENBQUFBLE1BQ3JDQSxJQUFJVCxFQUFFLEtBQUt2QixNQUFNRSxZQUFZLENBQUVxQixFQUFFLEdBQUdDLGVBQWVRO2dCQUdyRC9CLFNBQVNZLENBQUFBLE9BQVM7d0JBQ2hCLEdBQUdBLElBQUk7d0JBQ1BWLFFBQVEyQjt3QkFDUjVCLGNBQWNzQjtvQkFDaEI7Z0JBRUEsd0JBQXdCO2dCQUN4QmpCO1lBQ0YsT0FBTztnQkFDTE4sU0FBU1ksQ0FBQUEsT0FBUzt3QkFBRSxHQUFHQSxJQUFJO3dCQUFFUCxPQUFPRyxTQUFTSCxLQUFLLElBQUk7b0JBQWtDO1lBQzFGO1FBQ0YsRUFBRSxPQUFPUSxLQUFLO1lBQ1piLFNBQVNZLENBQUFBLE9BQVM7b0JBQUUsR0FBR0EsSUFBSTtvQkFBRVAsT0FBTztnQkFBa0M7WUFDdEVTLFFBQVFULEtBQUssQ0FBQyxrQ0FBa0NRO1FBQ2xEO0lBQ0Y7SUFFQSxNQUFNdUIsWUFBWSxDQUFDQztRQUNqQixJQUFJQSxTQUFTLEtBQUtBLFFBQVF0QyxNQUFNRyxNQUFNLENBQUNjLE1BQU0sRUFBRTtZQUM3Q2hCLFNBQVNZLENBQUFBLE9BQVM7b0JBQ2hCLEdBQUdBLElBQUk7b0JBQ1BULGNBQWNrQztvQkFDZHBDLGNBQWNXLEtBQUtWLE1BQU0sQ0FBQ21DLE1BQU07Z0JBQ2xDO1FBQ0Y7SUFDRjtJQUVBLE1BQU1KLHVCQUF1QjtRQUMzQixNQUFNSyxtQkFBbUJ2QyxNQUFNRyxNQUFNLENBQUNxQyxTQUFTLENBQUMsQ0FBQ1IsS0FBS00sUUFDcERBLFFBQVF0QyxNQUFNSSxZQUFZLElBQUk0QixJQUFJUCxNQUFNLEtBQUs7UUFHL0MsSUFBSWMscUJBQXFCLENBQUMsR0FBRztZQUMzQkYsVUFBVUU7UUFDWixPQUFPO1lBQ0wsMEJBQTBCO1lBQzFCLE1BQU1FLG9CQUFvQnpDLE1BQU1HLE1BQU0sQ0FBQ3FDLFNBQVMsQ0FBQ1IsQ0FBQUEsTUFBT0EsSUFBSVAsTUFBTSxLQUFLO1lBQ3ZFLElBQUlnQixzQkFBc0IsQ0FBQyxHQUFHO2dCQUM1QkosVUFBVUk7WUFDWjtRQUNGO0lBQ0Y7SUFFQSxNQUFNQyxlQUFlO1FBQ25CLElBQUkxQyxNQUFNSSxZQUFZLEdBQUcsR0FBRztZQUMxQmlDLFVBQVVyQyxNQUFNSSxZQUFZLEdBQUc7UUFDakM7SUFDRjtJQUVBLE1BQU11QyxXQUFXO1FBQ2YsSUFBSTNDLE1BQU1JLFlBQVksR0FBR0osTUFBTUcsTUFBTSxDQUFDYyxNQUFNLEdBQUcsR0FBRztZQUNoRG9CLFVBQVVyQyxNQUFNSSxZQUFZLEdBQUc7UUFDakM7SUFDRjtJQUVBLHNCQUFzQjtJQUN0QlgsZ0RBQVNBOzZDQUFDO1lBQ1IsTUFBTW1EO29FQUFpQixDQUFDQztvQkFDdEIsSUFBSSxDQUFDL0MsU0FBUztvQkFFZCxJQUFJK0MsRUFBRUMsR0FBRyxJQUFJLE9BQU9ELEVBQUVDLEdBQUcsSUFBSSxLQUFLO3dCQUNoQyxNQUFNQyxhQUFhQyxTQUFTSCxFQUFFQyxHQUFHLElBQUk7d0JBQ3JDLElBQUlDLGFBQWFqRCxRQUFRbUQsT0FBTyxDQUFDaEMsTUFBTSxFQUFFOzRCQUN2Q0MsY0FBY3BCLFFBQVFtRCxPQUFPLENBQUNGLFdBQVc7d0JBQzNDO29CQUNGLE9BQU8sSUFBSUYsRUFBRUMsR0FBRyxLQUFLLGFBQWE7d0JBQ2hDSjtvQkFDRixPQUFPLElBQUlHLEVBQUVDLEdBQUcsS0FBSyxjQUFjO3dCQUNqQ0g7b0JBQ0YsT0FBTyxJQUFJRSxFQUFFQyxHQUFHLEtBQUssZUFBZUQsRUFBRUMsR0FBRyxLQUFLLFVBQVU7d0JBQ3REWDtvQkFDRjtnQkFDRjs7WUFFQWUsT0FBT0MsZ0JBQWdCLENBQUMsV0FBV1A7WUFDbkM7cURBQU8sSUFBTU0sT0FBT0UsbUJBQW1CLENBQUMsV0FBV1I7O1FBQ3JEOzRDQUFHO1FBQUM5QztRQUFTRSxNQUFNSSxZQUFZO1FBQUVKLE1BQU1FLFlBQVk7S0FBQztJQUVwRCxJQUFJRixNQUFNSyxTQUFTLEVBQUU7UUFDbkIscUJBQ0UsOERBQUNnRDtZQUFJbEMsV0FBVTtzQkFDYiw0RUFBQ2tDO2dCQUFJbEMsV0FBVTs7Ozs7Ozs7Ozs7SUFHckI7SUFFQSxJQUFJbkIsTUFBTU0sS0FBSyxFQUFFO1FBQ2YscUJBQ0UsOERBQUMrQztZQUFJbEMsV0FBVTtzQkFDWm5CLE1BQU1NLEtBQUs7Ozs7OztJQUdsQjtJQUVBLElBQUksQ0FBQ1IsU0FBUztRQUNaLHFCQUNFLDhEQUFDdUQ7WUFBSWxDLFdBQVU7OzhCQUNiLDhEQUFDbUM7b0JBQUduQyxXQUFVOzhCQUF5Qzs7Ozs7OzhCQUN2RCw4REFBQ3pCLGtEQUFJQTtvQkFBQzZELE1BQUs7b0JBQUlwQyxXQUFVOzhCQUFvQzs7Ozs7Ozs7Ozs7O0lBS25FO0lBRUEsSUFBSW5CLE1BQU1HLE1BQU0sQ0FBQ2MsTUFBTSxLQUFLLEdBQUc7UUFDN0IscUJBQ0UsOERBQUNvQztZQUFJbEMsV0FBVTs7OEJBQ2IsOERBQUNrQztvQkFBSWxDLFdBQVU7OEJBQThCOzs7Ozs7OEJBQzdDLDhEQUFDbUM7b0JBQUduQyxXQUFVOzhCQUF5Qzs7Ozs7OzhCQUN2RCw4REFBQ3FDO29CQUFFckMsV0FBVTs4QkFBcUI7Ozs7Ozs4QkFHbEMsOERBQUN6QixrREFBSUE7b0JBQ0g2RCxNQUFNLGFBQXVCLE9BQVYxRDtvQkFDbkJzQixXQUFVOzhCQUNYOzs7Ozs7Ozs7Ozs7SUFLUDtJQUVBLE1BQU1zQyxvQkFBb0J6RCxNQUFNRyxNQUFNLENBQUN1RCxNQUFNLENBQUMxQixDQUFBQSxNQUFPQSxJQUFJUCxNQUFNLEtBQUssZ0JBQWdCUixNQUFNO0lBQzFGLE1BQU0wQyxxQkFBcUJDLEtBQUtDLEtBQUssQ0FBQyxDQUFFN0QsTUFBTUcsTUFBTSxDQUFDYyxNQUFNLEdBQUd3QyxpQkFBZ0IsSUFBS3pELE1BQU1HLE1BQU0sQ0FBQ2MsTUFBTSxHQUFJO0lBRTFHLHFCQUNFLDhEQUFDb0M7UUFBSWxDLFdBQVU7OzBCQUViLDhEQUFDa0M7Z0JBQUlsQyxXQUFVOztrQ0FDYiw4REFBQ2tDOzswQ0FDQyw4REFBQzNELGtEQUFJQTtnQ0FDSDZELE1BQU0sYUFBdUIsT0FBVjFEO2dDQUNuQnNCLFdBQVU7O29DQUNYO29DQUNZckIsUUFBUWdFLElBQUk7Ozs7Ozs7MENBRXpCLDhEQUFDQztnQ0FBRzVDLFdBQVU7MENBQW1DOzs7Ozs7Ozs7Ozs7a0NBRW5ELDhEQUFDa0M7d0JBQUlsQyxXQUFVOzswQ0FDYiw4REFBQ2tDO2dDQUFJbEMsV0FBVTs7b0NBQXdCO29DQUM5Qm5CLE1BQU1JLFlBQVksR0FBRztvQ0FBRTtvQ0FBS0osTUFBTUcsTUFBTSxDQUFDYyxNQUFNOzs7Ozs7OzBDQUV4RCw4REFBQ29DO2dDQUFJbEMsV0FBVTs7b0NBQ1pzQztvQ0FBa0I7b0NBQWNFO29DQUFtQjs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFNMUQsOERBQUNOO2dCQUFJbEMsV0FBVTswQkFDYiw0RUFBQ2tDO29CQUFJbEMsV0FBVTs4QkFDYiw0RUFBQ2tDO3dCQUNDbEMsV0FBVTt3QkFDVjZDLE9BQU87NEJBQUVDLE9BQU8sR0FBc0IsT0FBbkJOLG9CQUFtQjt3QkFBRzs7Ozs7Ozs7Ozs7Ozs7OztZQU05QzNELE1BQU1NLEtBQUssa0JBQ1YsOERBQUMrQztnQkFBSWxDLFdBQVU7O29CQUNabkIsTUFBTU0sS0FBSztrQ0FDWiw4REFBQzREO3dCQUNDQyxTQUFTLElBQU1sRSxTQUFTWSxDQUFBQSxPQUFTO29DQUFFLEdBQUdBLElBQUk7b0NBQUVQLE9BQU87Z0NBQUs7d0JBQ3hEYSxXQUFVO2tDQUNYOzs7Ozs7Ozs7Ozs7MEJBTUwsOERBQUNrQztnQkFBSWxDLFdBQVU7O2tDQUViLDhEQUFDa0M7d0JBQUlsQyxXQUFVO2tDQUNabkIsTUFBTUUsWUFBWSxrQkFDakIsOERBQUNtRDs0QkFBSWxDLFdBQVU7OzhDQUNiLDhEQUFDa0M7b0NBQUlsQyxXQUFVOzhDQUNiLDRFQUFDYTt3Q0FDQ29DLEtBQUt6RSxxREFBVUEsQ0FBQzBFLFdBQVcsQ0FBQ3JFLE1BQU1FLFlBQVksQ0FBQ29FLFNBQVM7d0NBQ3hEQyxLQUFLdkUsTUFBTUUsWUFBWSxDQUFDc0UsaUJBQWlCO3dDQUN6Q3JELFdBQVU7Ozs7Ozs7Ozs7OzhDQUlkLDhEQUFDa0M7b0NBQUlsQyxXQUFVOztzREFDYiw4REFBQ2tDOzs4REFDQyw4REFBQ0M7b0RBQUduQyxXQUFVOzhEQUE2Qm5CLE1BQU1FLFlBQVksQ0FBQ3NFLGlCQUFpQjs7Ozs7O2dEQUM5RXhFLE1BQU1FLFlBQVksQ0FBQ2tCLGNBQWMsa0JBQ2hDLDhEQUFDaUM7b0RBQUlsQyxXQUFVOzt3REFBOEI7c0VBQzVCLDhEQUFDc0Q7NERBQUt0RCxXQUFVO3NFQUFlbkIsTUFBTUUsWUFBWSxDQUFDa0IsY0FBYyxDQUFDQyxVQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7d0NBSS9GckIsTUFBTUUsWUFBWSxDQUFDa0IsY0FBYyxrQkFDaEMsOERBQUM4Qzs0Q0FDQ0MsU0FBU2hDOzRDQUNUaEIsV0FBVTtzREFDWDs7Ozs7Ozs7Ozs7OzhDQU9MLDhEQUFDa0M7b0NBQUlsQyxXQUFVOztzREFDYiw4REFBQytDOzRDQUNDQyxTQUFTekI7NENBQ1RnQyxVQUFVMUUsTUFBTUksWUFBWSxLQUFLOzRDQUNqQ2UsV0FBVTtzREFDWDs7Ozs7O3NEQUlELDhEQUFDK0M7NENBQ0NDLFNBQVNqQzs0Q0FDVGYsV0FBVTtzREFDWDs7Ozs7O3NEQUlELDhEQUFDK0M7NENBQ0NDLFNBQVN4Qjs0Q0FDVCtCLFVBQVUxRSxNQUFNSSxZQUFZLEtBQUtKLE1BQU1HLE1BQU0sQ0FBQ2MsTUFBTSxHQUFHOzRDQUN2REUsV0FBVTtzREFDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBU1QsOERBQUNrQzt3QkFBSWxDLFdBQVU7OzBDQUViLDhEQUFDa0M7Z0NBQUlsQyxXQUFVOztrREFDYiw4REFBQ21DO3dDQUFHbkMsV0FBVTtrREFBMkM7Ozs7OztrREFDekQsOERBQUNrQzt3Q0FBSWxDLFdBQVU7a0RBQ1pyQixRQUFRbUQsT0FBTyxDQUFDbEIsR0FBRyxDQUFDLENBQUNaLFdBQVdtQjtnREFLM0J0QyxvQ0FBQUE7aUVBSkosOERBQUNrRTtnREFFQ0MsU0FBUyxJQUFNakQsY0FBY0M7Z0RBQzdCQSxXQUFXLHVFQUlWLE9BSENuQixFQUFBQSxzQkFBQUEsTUFBTUUsWUFBWSxjQUFsQkYsMkNBQUFBLHFDQUFBQSxvQkFBb0JvQixjQUFjLGNBQWxDcEIseURBQUFBLG1DQUFvQ3FCLFVBQVUsTUFBS0YsWUFDL0MsNEJBQ0E7O2tFQUdOLDhEQUFDc0Q7d0RBQUt0RCxXQUFVOzs0REFBNkI7NERBQUVtQixRQUFROzREQUFFOzs7Ozs7O29EQUN4RG5COzsrQ0FUSUE7Ozs7Ozs7Ozs7O2tEQWFYLDhEQUFDa0M7d0NBQUlsQyxXQUFVOzs0Q0FBNkI7NENBQ2ZyQixRQUFRbUQsT0FBTyxDQUFDaEMsTUFBTTs0Q0FBQzs7Ozs7Ozs7Ozs7OzswQ0FLdEQsOERBQUNvQztnQ0FBSWxDLFdBQVU7O2tEQUNiLDhEQUFDbUM7d0NBQUduQyxXQUFVO2tEQUEyQzs7Ozs7O2tEQUN6RCw4REFBQ2tDO3dDQUFJbEMsV0FBVTtrREFDWm5CLE1BQU1HLE1BQU0sQ0FBQzRCLEdBQUcsQ0FBQyxDQUFDNEMsT0FBT3JDLHNCQUN4Qiw4REFBQzRCO2dEQUVDQyxTQUFTLElBQU05QixVQUFVQztnREFDekJuQixXQUFXLDZFQU1WLE9BTENtQixVQUFVdEMsTUFBTUksWUFBWSxHQUN4QixvQkFDQXVFLE1BQU1sRCxNQUFNLEtBQUssZUFDakIscUJBQ0E7O2tFQUdOLDhEQUFDTzt3REFDQ29DLEtBQUt6RSxxREFBVUEsQ0FBQzBFLFdBQVcsQ0FBQ00sTUFBTUwsU0FBUzt3REFDM0NDLEtBQUtJLE1BQU1ILGlCQUFpQjt3REFDNUJyRCxXQUFVOzs7Ozs7b0RBRVh3RCxNQUFNbEQsTUFBTSxLQUFLLDhCQUNoQiw4REFBQzRCO3dEQUFJbEMsV0FBVTtrRUFDYiw0RUFBQ2tDOzREQUFJbEMsV0FBVTtzRUFBc0Q7Ozs7Ozs7Ozs7OzsrQ0FqQnBFd0QsTUFBTXBELEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUE0Qi9CO0dBOWF3QjNCO0tBQUFBIiwic291cmNlcyI6WyIvVXNlcnMva2FkZG8vRG93bmxvYWRzL2NsYXNzaWZ5LXRlc3QvZnJvbnRlbmQvc3JjL2NvbXBvbmVudHMvQ2xhc3NpZmljYXRpb25JbnRlcmZhY2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCBMaW5rIGZyb20gJ25leHQvbGluayc7XG5pbXBvcnQgeyBQcm9qZWN0LCBJbWFnZVdpdGhDbGFzc2lmaWNhdGlvbiwgQ2xhc3NpZmljYXRpb25TdGF0ZSB9IGZyb20gJ0AvdHlwZXMnO1xuaW1wb3J0IHsgYXBpU2VydmljZSB9IGZyb20gJ0Avc2VydmljZXMvYXBpJztcblxuaW50ZXJmYWNlIENsYXNzaWZpY2F0aW9uSW50ZXJmYWNlUHJvcHMge1xuICBwcm9qZWN0SWQ6IHN0cmluZztcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQ2xhc3NpZmljYXRpb25JbnRlcmZhY2UoeyBwcm9qZWN0SWQgfTogQ2xhc3NpZmljYXRpb25JbnRlcmZhY2VQcm9wcykge1xuICBjb25zdCBbcHJvamVjdCwgc2V0UHJvamVjdF0gPSB1c2VTdGF0ZTxQcm9qZWN0IHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtzdGF0ZSwgc2V0U3RhdGVdID0gdXNlU3RhdGU8Q2xhc3NpZmljYXRpb25TdGF0ZT4oe1xuICAgIGN1cnJlbnRJbWFnZTogbnVsbCxcbiAgICBpbWFnZXM6IFtdLFxuICAgIGN1cnJlbnRJbmRleDogMCxcbiAgICBpc0xvYWRpbmc6IHRydWUsXG4gICAgZXJyb3I6IG51bGxcbiAgfSk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBsb2FkUHJvamVjdCgpO1xuICAgIGxvYWRJbWFnZXMoKTtcbiAgfSwgW3Byb2plY3RJZF0pO1xuXG4gIGNvbnN0IGxvYWRQcm9qZWN0ID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaVNlcnZpY2UuZ2V0UHJvamVjdChwcm9qZWN0SWQpO1xuICAgICAgaWYgKHJlc3BvbnNlLnN1Y2Nlc3MpIHtcbiAgICAgICAgc2V0UHJvamVjdChyZXNwb25zZS5kYXRhKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHNldFN0YXRlKHByZXYgPT4gKHsgLi4ucHJldiwgZXJyb3I6IHJlc3BvbnNlLmVycm9yIHx8ICdGYWlsZWQgdG8gbG9hZCBwcm9qZWN0JyB9KSk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBzZXRTdGF0ZShwcmV2ID0+ICh7IC4uLnByZXYsIGVycm9yOiAnRmFpbGVkIHRvIGxvYWQgcHJvamVjdCcgfSkpO1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgbG9hZGluZyBwcm9qZWN0OicsIGVycik7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGxvYWRJbWFnZXMgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldFN0YXRlKHByZXYgPT4gKHsgLi4ucHJldiwgaXNMb2FkaW5nOiB0cnVlLCBlcnJvcjogbnVsbCB9KSk7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaVNlcnZpY2UuZ2V0UHJvamVjdEltYWdlc1dpdGhDbGFzc2lmaWNhdGlvbnMocHJvamVjdElkKTtcbiAgICAgIFxuICAgICAgaWYgKHJlc3BvbnNlLnN1Y2Nlc3MgJiYgcmVzcG9uc2UuZGF0YSkge1xuICAgICAgICBjb25zdCBpbWFnZXMgPSByZXNwb25zZS5kYXRhO1xuICAgICAgICBzZXRTdGF0ZShwcmV2ID0+ICh7XG4gICAgICAgICAgLi4ucHJldixcbiAgICAgICAgICBpbWFnZXMsXG4gICAgICAgICAgY3VycmVudEltYWdlOiBpbWFnZXMubGVuZ3RoID4gMCA/IGltYWdlc1swXSA6IG51bGwsXG4gICAgICAgICAgY3VycmVudEluZGV4OiAwLFxuICAgICAgICAgIGlzTG9hZGluZzogZmFsc2VcbiAgICAgICAgfSkpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgc2V0U3RhdGUocHJldiA9PiAoeyBcbiAgICAgICAgICAuLi5wcmV2LCBcbiAgICAgICAgICBlcnJvcjogcmVzcG9uc2UuZXJyb3IgfHwgJ0ZhaWxlZCB0byBsb2FkIGltYWdlcycsXG4gICAgICAgICAgaXNMb2FkaW5nOiBmYWxzZSBcbiAgICAgICAgfSkpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgc2V0U3RhdGUocHJldiA9PiAoeyBcbiAgICAgICAgLi4ucHJldiwgXG4gICAgICAgIGVycm9yOiAnRmFpbGVkIHRvIGxvYWQgaW1hZ2VzJyxcbiAgICAgICAgaXNMb2FkaW5nOiBmYWxzZSBcbiAgICAgIH0pKTtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGxvYWRpbmcgaW1hZ2VzOicsIGVycik7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGNsYXNzaWZ5SW1hZ2UgPSBhc3luYyAoY2xhc3NOYW1lOiBzdHJpbmcpID0+IHtcbiAgICBpZiAoIXN0YXRlLmN1cnJlbnRJbWFnZSkgcmV0dXJuO1xuXG4gICAgLy8gQ2hlY2sgaWYgaW1hZ2UgaXMgYWxyZWFkeSBjbGFzc2lmaWVkIHdpdGggdGhlIHNhbWUgY2xhc3NcbiAgICBpZiAoc3RhdGUuY3VycmVudEltYWdlLmNsYXNzaWZpY2F0aW9uPy5jbGFzc19uYW1lID09PSBjbGFzc05hbWUpIHtcbiAgICAgIHJldHVybjsgLy8gQWxyZWFkeSBjbGFzc2lmaWVkIHdpdGggdGhpcyBjbGFzcywgZG8gbm90aGluZ1xuICAgIH1cblxuICAgIHRyeSB7XG4gICAgICBzZXRTdGF0ZShwcmV2ID0+ICh7IC4uLnByZXYsIGVycm9yOiBudWxsIH0pKTsgLy8gQ2xlYXIgYW55IHByZXZpb3VzIGVycm9yc1xuXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaVNlcnZpY2UuY2xhc3NpZnlJbWFnZSh7XG4gICAgICAgIGltYWdlX2lkOiBzdGF0ZS5jdXJyZW50SW1hZ2UuaWQsXG4gICAgICAgIGNsYXNzX25hbWU6IGNsYXNzTmFtZVxuICAgICAgfSk7XG5cbiAgICAgIGlmIChyZXNwb25zZS5zdWNjZXNzKSB7XG4gICAgICAgIC8vIFVwZGF0ZSB0aGUgY3VycmVudCBpbWFnZSB3aXRoIGNsYXNzaWZpY2F0aW9uXG4gICAgICAgIGNvbnN0IHVwZGF0ZWRJbWFnZTogSW1hZ2VXaXRoQ2xhc3NpZmljYXRpb24gPSB7XG4gICAgICAgICAgLi4uc3RhdGUuY3VycmVudEltYWdlLFxuICAgICAgICAgIHN0YXR1czogJ2NsYXNzaWZpZWQnLFxuICAgICAgICAgIGNsYXNzaWZpY2F0aW9uOiB7XG4gICAgICAgICAgICBpZDogcmVzcG9uc2UuZGF0YS5pZCxcbiAgICAgICAgICAgIGltYWdlX2lkOiBzdGF0ZS5jdXJyZW50SW1hZ2UuaWQsXG4gICAgICAgICAgICBwcm9qZWN0X2lkOiBwcm9qZWN0SWQsXG4gICAgICAgICAgICBjbGFzc19uYW1lOiBjbGFzc05hbWUsXG4gICAgICAgICAgICBjbGFzc2lmaWVkX2F0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICAgICAgICB9XG4gICAgICAgIH07XG5cbiAgICAgICAgLy8gVXBkYXRlIGltYWdlcyBhcnJheVxuICAgICAgICBjb25zdCB1cGRhdGVkSW1hZ2VzID0gc3RhdGUuaW1hZ2VzLm1hcChpbWcgPT5cbiAgICAgICAgICBpbWcuaWQgPT09IHN0YXRlLmN1cnJlbnRJbWFnZSEuaWQgPyB1cGRhdGVkSW1hZ2UgOiBpbWdcbiAgICAgICAgKTtcblxuICAgICAgICBzZXRTdGF0ZShwcmV2ID0+ICh7XG4gICAgICAgICAgLi4ucHJldixcbiAgICAgICAgICBpbWFnZXM6IHVwZGF0ZWRJbWFnZXMsXG4gICAgICAgICAgY3VycmVudEltYWdlOiB1cGRhdGVkSW1hZ2UsXG4gICAgICAgICAgZXJyb3I6IG51bGxcbiAgICAgICAgfSkpO1xuXG4gICAgICAgIC8vIEF1dG8tYWR2YW5jZSB0byBuZXh0IHVuY2xhc3NpZmllZCBpbWFnZSBhZnRlciBhIHNob3J0IGRlbGF5XG4gICAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICAgIGdvVG9OZXh0VW5jbGFzc2lmaWVkKCk7XG4gICAgICAgIH0sIDMwMCk7XG5cbiAgICAgICAgLy8gUmVmcmVzaCBwcm9qZWN0IHN0YXRzXG4gICAgICAgIGxvYWRQcm9qZWN0KCk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBzZXRTdGF0ZShwcmV2ID0+ICh7IC4uLnByZXYsIGVycm9yOiByZXNwb25zZS5lcnJvciB8fCAnRmFpbGVkIHRvIGNsYXNzaWZ5IGltYWdlJyB9KSk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBzZXRTdGF0ZShwcmV2ID0+ICh7IC4uLnByZXYsIGVycm9yOiAnRmFpbGVkIHRvIGNsYXNzaWZ5IGltYWdlJyB9KSk7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBjbGFzc2lmeWluZyBpbWFnZTonLCBlcnIpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCByZW1vdmVDbGFzc2lmaWNhdGlvbiA9IGFzeW5jICgpID0+IHtcbiAgICBpZiAoIXN0YXRlLmN1cnJlbnRJbWFnZSB8fCAhc3RhdGUuY3VycmVudEltYWdlLmNsYXNzaWZpY2F0aW9uKSByZXR1cm47XG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGlTZXJ2aWNlLnJlbW92ZUNsYXNzaWZpY2F0aW9uKHN0YXRlLmN1cnJlbnRJbWFnZS5pZCk7XG5cbiAgICAgIGlmIChyZXNwb25zZS5zdWNjZXNzKSB7XG4gICAgICAgIC8vIFVwZGF0ZSB0aGUgY3VycmVudCBpbWFnZSB0byByZW1vdmUgY2xhc3NpZmljYXRpb25cbiAgICAgICAgY29uc3QgdXBkYXRlZEltYWdlOiBJbWFnZVdpdGhDbGFzc2lmaWNhdGlvbiA9IHtcbiAgICAgICAgICAuLi5zdGF0ZS5jdXJyZW50SW1hZ2UsXG4gICAgICAgICAgc3RhdHVzOiAndW5jbGFzc2lmaWVkJyxcbiAgICAgICAgICBjbGFzc2lmaWNhdGlvbjogdW5kZWZpbmVkXG4gICAgICAgIH07XG5cbiAgICAgICAgLy8gVXBkYXRlIGltYWdlcyBhcnJheVxuICAgICAgICBjb25zdCB1cGRhdGVkSW1hZ2VzID0gc3RhdGUuaW1hZ2VzLm1hcChpbWcgPT4gXG4gICAgICAgICAgaW1nLmlkID09PSBzdGF0ZS5jdXJyZW50SW1hZ2UhLmlkID8gdXBkYXRlZEltYWdlIDogaW1nXG4gICAgICAgICk7XG5cbiAgICAgICAgc2V0U3RhdGUocHJldiA9PiAoe1xuICAgICAgICAgIC4uLnByZXYsXG4gICAgICAgICAgaW1hZ2VzOiB1cGRhdGVkSW1hZ2VzLFxuICAgICAgICAgIGN1cnJlbnRJbWFnZTogdXBkYXRlZEltYWdlXG4gICAgICAgIH0pKTtcblxuICAgICAgICAvLyBSZWZyZXNoIHByb2plY3Qgc3RhdHNcbiAgICAgICAgbG9hZFByb2plY3QoKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHNldFN0YXRlKHByZXYgPT4gKHsgLi4ucHJldiwgZXJyb3I6IHJlc3BvbnNlLmVycm9yIHx8ICdGYWlsZWQgdG8gcmVtb3ZlIGNsYXNzaWZpY2F0aW9uJyB9KSk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBzZXRTdGF0ZShwcmV2ID0+ICh7IC4uLnByZXYsIGVycm9yOiAnRmFpbGVkIHRvIHJlbW92ZSBjbGFzc2lmaWNhdGlvbicgfSkpO1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgcmVtb3ZpbmcgY2xhc3NpZmljYXRpb246JywgZXJyKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgZ29Ub0ltYWdlID0gKGluZGV4OiBudW1iZXIpID0+IHtcbiAgICBpZiAoaW5kZXggPj0gMCAmJiBpbmRleCA8IHN0YXRlLmltYWdlcy5sZW5ndGgpIHtcbiAgICAgIHNldFN0YXRlKHByZXYgPT4gKHtcbiAgICAgICAgLi4ucHJldixcbiAgICAgICAgY3VycmVudEluZGV4OiBpbmRleCxcbiAgICAgICAgY3VycmVudEltYWdlOiBwcmV2LmltYWdlc1tpbmRleF1cbiAgICAgIH0pKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgZ29Ub05leHRVbmNsYXNzaWZpZWQgPSAoKSA9PiB7XG4gICAgY29uc3QgbmV4dFVuY2xhc3NpZmllZCA9IHN0YXRlLmltYWdlcy5maW5kSW5kZXgoKGltZywgaW5kZXgpID0+IFxuICAgICAgaW5kZXggPiBzdGF0ZS5jdXJyZW50SW5kZXggJiYgaW1nLnN0YXR1cyA9PT0gJ3VuY2xhc3NpZmllZCdcbiAgICApO1xuICAgIFxuICAgIGlmIChuZXh0VW5jbGFzc2lmaWVkICE9PSAtMSkge1xuICAgICAgZ29Ub0ltYWdlKG5leHRVbmNsYXNzaWZpZWQpO1xuICAgIH0gZWxzZSB7XG4gICAgICAvLyBMb29rIGZyb20gdGhlIGJlZ2lubmluZ1xuICAgICAgY29uc3QgZmlyc3RVbmNsYXNzaWZpZWQgPSBzdGF0ZS5pbWFnZXMuZmluZEluZGV4KGltZyA9PiBpbWcuc3RhdHVzID09PSAndW5jbGFzc2lmaWVkJyk7XG4gICAgICBpZiAoZmlyc3RVbmNsYXNzaWZpZWQgIT09IC0xKSB7XG4gICAgICAgIGdvVG9JbWFnZShmaXJzdFVuY2xhc3NpZmllZCk7XG4gICAgICB9XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGdvVG9QcmV2aW91cyA9ICgpID0+IHtcbiAgICBpZiAoc3RhdGUuY3VycmVudEluZGV4ID4gMCkge1xuICAgICAgZ29Ub0ltYWdlKHN0YXRlLmN1cnJlbnRJbmRleCAtIDEpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBnb1RvTmV4dCA9ICgpID0+IHtcbiAgICBpZiAoc3RhdGUuY3VycmVudEluZGV4IDwgc3RhdGUuaW1hZ2VzLmxlbmd0aCAtIDEpIHtcbiAgICAgIGdvVG9JbWFnZShzdGF0ZS5jdXJyZW50SW5kZXggKyAxKTtcbiAgICB9XG4gIH07XG5cbiAgLy8gS2V5Ym9hcmQgbmF2aWdhdGlvblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGhhbmRsZUtleVByZXNzID0gKGU6IEtleWJvYXJkRXZlbnQpID0+IHtcbiAgICAgIGlmICghcHJvamVjdCkgcmV0dXJuO1xuXG4gICAgICBpZiAoZS5rZXkgPj0gJzEnICYmIGUua2V5IDw9ICc5Jykge1xuICAgICAgICBjb25zdCBjbGFzc0luZGV4ID0gcGFyc2VJbnQoZS5rZXkpIC0gMTtcbiAgICAgICAgaWYgKGNsYXNzSW5kZXggPCBwcm9qZWN0LmNsYXNzZXMubGVuZ3RoKSB7XG4gICAgICAgICAgY2xhc3NpZnlJbWFnZShwcm9qZWN0LmNsYXNzZXNbY2xhc3NJbmRleF0pO1xuICAgICAgICB9XG4gICAgICB9IGVsc2UgaWYgKGUua2V5ID09PSAnQXJyb3dMZWZ0Jykge1xuICAgICAgICBnb1RvUHJldmlvdXMoKTtcbiAgICAgIH0gZWxzZSBpZiAoZS5rZXkgPT09ICdBcnJvd1JpZ2h0Jykge1xuICAgICAgICBnb1RvTmV4dCgpO1xuICAgICAgfSBlbHNlIGlmIChlLmtleSA9PT0gJ0JhY2tzcGFjZScgfHwgZS5rZXkgPT09ICdEZWxldGUnKSB7XG4gICAgICAgIHJlbW92ZUNsYXNzaWZpY2F0aW9uKCk7XG4gICAgICB9XG4gICAgfTtcblxuICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdrZXlkb3duJywgaGFuZGxlS2V5UHJlc3MpO1xuICAgIHJldHVybiAoKSA9PiB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcigna2V5ZG93bicsIGhhbmRsZUtleVByZXNzKTtcbiAgfSwgW3Byb2plY3QsIHN0YXRlLmN1cnJlbnRJbmRleCwgc3RhdGUuY3VycmVudEltYWdlXSk7XG5cbiAgaWYgKHN0YXRlLmlzTG9hZGluZykge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIG1pbi1oLTY0XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTEyIHctMTIgYm9yZGVyLWItMiBib3JkZXItYmx1ZS02MDBcIj48L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH1cblxuICBpZiAoc3RhdGUuZXJyb3IpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1yZWQtNTAgYm9yZGVyIGJvcmRlci1yZWQtMjAwIHRleHQtcmVkLTcwMCBweC00IHB5LTMgcm91bmRlZC1sZ1wiPlxuICAgICAgICB7c3RhdGUuZXJyb3J9XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9XG5cbiAgaWYgKCFwcm9qZWN0KSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcHktMTJcIj5cbiAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCBtYi0yXCI+UHJvamVjdCBub3QgZm91bmQ8L2gzPlxuICAgICAgICA8TGluayBocmVmPVwiL1wiIGNsYXNzTmFtZT1cInRleHQtYmx1ZS02MDAgaG92ZXI6dGV4dC1ibHVlLTcwMFwiPlxuICAgICAgICAgIEJhY2sgdG8gUHJvamVjdHNcbiAgICAgICAgPC9MaW5rPlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIGlmIChzdGF0ZS5pbWFnZXMubGVuZ3RoID09PSAwKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcHktMTJcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIHRleHQtNnhsIG1iLTRcIj7wn5O3PC9kaXY+XG4gICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgbWItMlwiPk5vIGltYWdlcyB0byBjbGFzc2lmeTwvaDM+XG4gICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgbWItNlwiPlxuICAgICAgICAgIFVwbG9hZCBpbWFnZXMgdG8gdGhpcyBwcm9qZWN0IHRvIHN0YXJ0IGNsYXNzaWZ5aW5nXG4gICAgICAgIDwvcD5cbiAgICAgICAgPExpbmtcbiAgICAgICAgICBocmVmPXtgL3Byb2plY3RzLyR7cHJvamVjdElkfWB9XG4gICAgICAgICAgY2xhc3NOYW1lPVwiYmctYmx1ZS02MDAgaG92ZXI6YmctYmx1ZS03MDAgdGV4dC13aGl0ZSBweC02IHB5LTMgcm91bmRlZC1sZyBmb250LW1lZGl1bSB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgID5cbiAgICAgICAgICBCYWNrIHRvIFByb2plY3RcbiAgICAgICAgPC9MaW5rPlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIGNvbnN0IHVuY2xhc3NpZmllZENvdW50ID0gc3RhdGUuaW1hZ2VzLmZpbHRlcihpbWcgPT4gaW1nLnN0YXR1cyA9PT0gJ3VuY2xhc3NpZmllZCcpLmxlbmd0aDtcbiAgY29uc3QgcHJvZ3Jlc3NQZXJjZW50YWdlID0gTWF0aC5yb3VuZCgoKHN0YXRlLmltYWdlcy5sZW5ndGggLSB1bmNsYXNzaWZpZWRDb3VudCkgLyBzdGF0ZS5pbWFnZXMubGVuZ3RoKSAqIDEwMCk7XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTZ4bCBteC1hdXRvXCI+XG4gICAgICB7LyogSGVhZGVyICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXIgbWItNlwiPlxuICAgICAgICA8ZGl2PlxuICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICBocmVmPXtgL3Byb2plY3RzLyR7cHJvamVjdElkfWB9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtNjAwIGhvdmVyOnRleHQtYmx1ZS03MDAgbWItMiBpbmxpbmUtYmxvY2tcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIOKGkCBCYWNrIHRvIHtwcm9qZWN0Lm5hbWV9XG4gICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMFwiPkNsYXNzaWZpY2F0aW9uIEludGVyZmFjZTwvaDE+XG4gICAgICAgIDwvZGl2PlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtcmlnaHRcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgSW1hZ2Uge3N0YXRlLmN1cnJlbnRJbmRleCArIDF9IG9mIHtzdGF0ZS5pbWFnZXMubGVuZ3RofVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICB7dW5jbGFzc2lmaWVkQ291bnR9IHJlbWFpbmluZyDigKIge3Byb2dyZXNzUGVyY2VudGFnZX0lIGNvbXBsZXRlXG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBQcm9ncmVzcyBCYXIgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTZcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGwgYmctZ3JheS0yMDAgcm91bmRlZC1mdWxsIGgtMlwiPlxuICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWdyZWVuLTYwMCBoLTIgcm91bmRlZC1mdWxsIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMFwiXG4gICAgICAgICAgICBzdHlsZT17eyB3aWR0aDogYCR7cHJvZ3Jlc3NQZXJjZW50YWdlfSVgIH19XG4gICAgICAgICAgPjwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogRXJyb3IgRGlzcGxheSAqL31cbiAgICAgIHtzdGF0ZS5lcnJvciAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctcmVkLTUwIGJvcmRlciBib3JkZXItcmVkLTIwMCB0ZXh0LXJlZC03MDAgcHgtNCBweS0zIHJvdW5kZWQtbGcgbWItNlwiPlxuICAgICAgICAgIHtzdGF0ZS5lcnJvcn1cbiAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTdGF0ZShwcmV2ID0+ICh7IC4uLnByZXYsIGVycm9yOiBudWxsIH0pKX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cIm1sLTIgdGV4dC1yZWQtNTAwIGhvdmVyOnRleHQtcmVkLTcwMFwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgw5dcbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbGc6Z3JpZC1jb2xzLTMgZ2FwLTZcIj5cbiAgICAgICAgey8qIEltYWdlIERpc3BsYXkgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibGc6Y29sLXNwYW4tMlwiPlxuICAgICAgICAgIHtzdGF0ZS5jdXJyZW50SW1hZ2UgJiYgKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdy1zbSBib3JkZXIgYm9yZGVyLWdyYXktMjAwIHAtNlwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFzcGVjdC1zcXVhcmUgYmctZ3JheS0xMDAgcm91bmRlZC1sZyBvdmVyZmxvdy1oaWRkZW4gbWItNFwiPlxuICAgICAgICAgICAgICAgIDxpbWdcbiAgICAgICAgICAgICAgICAgIHNyYz17YXBpU2VydmljZS5nZXRJbWFnZVVybChzdGF0ZS5jdXJyZW50SW1hZ2UuZmlsZV9wYXRoKX1cbiAgICAgICAgICAgICAgICAgIGFsdD17c3RhdGUuY3VycmVudEltYWdlLm9yaWdpbmFsX2ZpbGVuYW1lfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGgtZnVsbCBvYmplY3QtY29udGFpblwiXG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlciBtYi00XCI+XG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwXCI+e3N0YXRlLmN1cnJlbnRJbWFnZS5vcmlnaW5hbF9maWxlbmFtZX08L2gzPlxuICAgICAgICAgICAgICAgICAge3N0YXRlLmN1cnJlbnRJbWFnZS5jbGFzc2lmaWNhdGlvbiAmJiAoXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyZWVuLTYwMCBtdC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgQ2xhc3NpZmllZCBhczogPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj57c3RhdGUuY3VycmVudEltYWdlLmNsYXNzaWZpY2F0aW9uLmNsYXNzX25hbWV9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAge3N0YXRlLmN1cnJlbnRJbWFnZS5jbGFzc2lmaWNhdGlvbiAmJiAoXG4gICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e3JlbW92ZUNsYXNzaWZpY2F0aW9ufVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXJlZC02MDAgaG92ZXI6dGV4dC1yZWQtNzAwIHRleHQtc21cIlxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICBSZW1vdmUgQ2xhc3NpZmljYXRpb25cbiAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIHsvKiBOYXZpZ2F0aW9uICovfVxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2dvVG9QcmV2aW91c31cbiAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtzdGF0ZS5jdXJyZW50SW5kZXggPT09IDB9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ncmF5LTIwMCBob3ZlcjpiZy1ncmF5LTMwMCB0ZXh0LWdyYXktODAwIHB4LTQgcHktMiByb3VuZGVkLWxnIGZvbnQtbWVkaXVtIHRyYW5zaXRpb24tY29sb3JzIGRpc2FibGVkOm9wYWNpdHktNTAgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICDihpAgUHJldmlvdXNcbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXtnb1RvTmV4dFVuY2xhc3NpZmllZH1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWJsdWUtNjAwIGhvdmVyOmJnLWJsdWUtNzAwIHRleHQtd2hpdGUgcHgtNCBweS0yIHJvdW5kZWQtbGcgZm9udC1tZWRpdW0gdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIE5leHQgVW5jbGFzc2lmaWVkXG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgb25DbGljaz17Z29Ub05leHR9XG4gICAgICAgICAgICAgICAgICBkaXNhYmxlZD17c3RhdGUuY3VycmVudEluZGV4ID09PSBzdGF0ZS5pbWFnZXMubGVuZ3RoIC0gMX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWdyYXktMjAwIGhvdmVyOmJnLWdyYXktMzAwIHRleHQtZ3JheS04MDAgcHgtNCBweS0yIHJvdW5kZWQtbGcgZm9udC1tZWRpdW0gdHJhbnNpdGlvbi1jb2xvcnMgZGlzYWJsZWQ6b3BhY2l0eS01MCBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWRcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIE5leHQg4oaSXG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIENsYXNzaWZpY2F0aW9uIFBhbmVsICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgICAgIHsvKiBDbGFzcyBCdXR0b25zICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC1sZyBzaGFkb3ctc20gYm9yZGVyIGJvcmRlci1ncmF5LTIwMCBwLTZcIj5cbiAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMCBtYi00XCI+Q2xhc3NpZnkgSW1hZ2U8L2gzPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAge3Byb2plY3QuY2xhc3Nlcy5tYXAoKGNsYXNzTmFtZSwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICBrZXk9e2NsYXNzTmFtZX1cbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGNsYXNzaWZ5SW1hZ2UoY2xhc3NOYW1lKX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHctZnVsbCB0ZXh0LWxlZnQgcHgtNCBweS0zIHJvdW5kZWQtbGcgZm9udC1tZWRpdW0gdHJhbnNpdGlvbi1jb2xvcnMgJHtcbiAgICAgICAgICAgICAgICAgICAgc3RhdGUuY3VycmVudEltYWdlPy5jbGFzc2lmaWNhdGlvbj8uY2xhc3NfbmFtZSA9PT0gY2xhc3NOYW1lXG4gICAgICAgICAgICAgICAgICAgICAgPyAnYmctZ3JlZW4tNjAwIHRleHQtd2hpdGUnXG4gICAgICAgICAgICAgICAgICAgICAgOiAnYmctZ3JheS0xMDAgaG92ZXI6YmctZ3JheS0yMDAgdGV4dC1ncmF5LTkwMCdcbiAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTUwMCBtci0yXCI+KHtpbmRleCArIDF9KTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIHtjbGFzc05hbWV9XG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTQgdGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgIFVzZSBrZXlib2FyZCBzaG9ydGN1dHM6IDEte3Byb2plY3QuY2xhc3Nlcy5sZW5ndGh9IHRvIGNsYXNzaWZ5LCDihpAg4oaSIHRvIG5hdmlnYXRlLCBCYWNrc3BhY2UgdG8gcmVtb3ZlXG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBJbWFnZSBUaHVtYm5haWxzICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC1sZyBzaGFkb3ctc20gYm9yZGVyIGJvcmRlci1ncmF5LTIwMCBwLTZcIj5cbiAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMCBtYi00XCI+QWxsIEltYWdlczwvaDM+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTQgZ2FwLTIgbWF4LWgtOTYgb3ZlcmZsb3cteS1hdXRvXCI+XG4gICAgICAgICAgICAgIHtzdGF0ZS5pbWFnZXMubWFwKChpbWFnZSwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICBrZXk9e2ltYWdlLmlkfVxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gZ29Ub0ltYWdlKGluZGV4KX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHJlbGF0aXZlIGFzcGVjdC1zcXVhcmUgcm91bmRlZCBvdmVyZmxvdy1oaWRkZW4gYm9yZGVyLTIgdHJhbnNpdGlvbi1jb2xvcnMgJHtcbiAgICAgICAgICAgICAgICAgICAgaW5kZXggPT09IHN0YXRlLmN1cnJlbnRJbmRleFxuICAgICAgICAgICAgICAgICAgICAgID8gJ2JvcmRlci1ibHVlLTUwMCdcbiAgICAgICAgICAgICAgICAgICAgICA6IGltYWdlLnN0YXR1cyA9PT0gJ2NsYXNzaWZpZWQnXG4gICAgICAgICAgICAgICAgICAgICAgPyAnYm9yZGVyLWdyZWVuLTUwMCdcbiAgICAgICAgICAgICAgICAgICAgICA6ICdib3JkZXItZ3JheS0yMDAnXG4gICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8aW1nXG4gICAgICAgICAgICAgICAgICAgIHNyYz17YXBpU2VydmljZS5nZXRJbWFnZVVybChpbWFnZS5maWxlX3BhdGgpfVxuICAgICAgICAgICAgICAgICAgICBhbHQ9e2ltYWdlLm9yaWdpbmFsX2ZpbGVuYW1lfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgaC1mdWxsIG9iamVjdC1jb3ZlclwiXG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAge2ltYWdlLnN0YXR1cyA9PT0gJ2NsYXNzaWZpZWQnICYmIChcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0b3AtMSByaWdodC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmVlbi02MDAgdGV4dC13aGl0ZSB0ZXh0LXhzIHB4LTEgcHktMC41IHJvdW5kZWRcIj7inJM8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiTGluayIsImFwaVNlcnZpY2UiLCJDbGFzc2lmaWNhdGlvbkludGVyZmFjZSIsInByb2plY3RJZCIsInByb2plY3QiLCJzZXRQcm9qZWN0Iiwic3RhdGUiLCJzZXRTdGF0ZSIsImN1cnJlbnRJbWFnZSIsImltYWdlcyIsImN1cnJlbnRJbmRleCIsImlzTG9hZGluZyIsImVycm9yIiwibG9hZFByb2plY3QiLCJsb2FkSW1hZ2VzIiwicmVzcG9uc2UiLCJnZXRQcm9qZWN0Iiwic3VjY2VzcyIsImRhdGEiLCJwcmV2IiwiZXJyIiwiY29uc29sZSIsImdldFByb2plY3RJbWFnZXNXaXRoQ2xhc3NpZmljYXRpb25zIiwibGVuZ3RoIiwiY2xhc3NpZnlJbWFnZSIsImNsYXNzTmFtZSIsImNsYXNzaWZpY2F0aW9uIiwiY2xhc3NfbmFtZSIsImltYWdlX2lkIiwiaWQiLCJ1cGRhdGVkSW1hZ2UiLCJzdGF0dXMiLCJwcm9qZWN0X2lkIiwiY2xhc3NpZmllZF9hdCIsIkRhdGUiLCJ0b0lTT1N0cmluZyIsInVwZGF0ZWRJbWFnZXMiLCJtYXAiLCJpbWciLCJzZXRUaW1lb3V0IiwiZ29Ub05leHRVbmNsYXNzaWZpZWQiLCJyZW1vdmVDbGFzc2lmaWNhdGlvbiIsInVuZGVmaW5lZCIsImdvVG9JbWFnZSIsImluZGV4IiwibmV4dFVuY2xhc3NpZmllZCIsImZpbmRJbmRleCIsImZpcnN0VW5jbGFzc2lmaWVkIiwiZ29Ub1ByZXZpb3VzIiwiZ29Ub05leHQiLCJoYW5kbGVLZXlQcmVzcyIsImUiLCJrZXkiLCJjbGFzc0luZGV4IiwicGFyc2VJbnQiLCJjbGFzc2VzIiwid2luZG93IiwiYWRkRXZlbnRMaXN0ZW5lciIsInJlbW92ZUV2ZW50TGlzdGVuZXIiLCJkaXYiLCJoMyIsImhyZWYiLCJwIiwidW5jbGFzc2lmaWVkQ291bnQiLCJmaWx0ZXIiLCJwcm9ncmVzc1BlcmNlbnRhZ2UiLCJNYXRoIiwicm91bmQiLCJuYW1lIiwiaDEiLCJzdHlsZSIsIndpZHRoIiwiYnV0dG9uIiwib25DbGljayIsInNyYyIsImdldEltYWdlVXJsIiwiZmlsZV9wYXRoIiwiYWx0Iiwib3JpZ2luYWxfZmlsZW5hbWUiLCJzcGFuIiwiZGlzYWJsZWQiLCJpbWFnZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ClassificationInterface.tsx\n"));

/***/ })

});