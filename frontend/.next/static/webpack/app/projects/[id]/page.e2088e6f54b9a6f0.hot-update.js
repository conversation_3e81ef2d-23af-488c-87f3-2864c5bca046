"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/page",{

/***/ "(app-pages-browser)/./src/components/ProjectDetail.tsx":
/*!******************************************!*\
  !*** ./src/components/ProjectDetail.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProjectDetail)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/api */ \"(app-pages-browser)/./src/services/api.ts\");\n/* harmony import */ var _UploadModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./UploadModal */ \"(app-pages-browser)/./src/components/UploadModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction ProjectDetail(param) {\n    let { projectId } = param;\n    _s();\n    const [project, setProject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [images, setImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showUploadModal, setShowUploadModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProjectDetail.useEffect\": ()=>{\n            loadProject();\n            loadImages();\n        }\n    }[\"ProjectDetail.useEffect\"], [\n        projectId\n    ]);\n    const loadProject = async ()=>{\n        try {\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_3__.apiService.getProject(projectId);\n            if (response.success) {\n                setProject(response.data);\n            } else {\n                setError(response.error || 'Failed to load project');\n            }\n        } catch (err) {\n            setError('Failed to load project');\n            console.error('Error loading project:', err);\n        }\n    };\n    const loadImages = async ()=>{\n        try {\n            setIsLoading(true);\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_3__.apiService.getProjectImagesWithClassifications(projectId);\n            if (response.success) {\n                setImages(response.data || []);\n            } else {\n                setError(response.error || 'Failed to load images');\n            }\n        } catch (err) {\n            setError('Failed to load images');\n            console.error('Error loading images:', err);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleUploadComplete = ()=>{\n        setShowUploadModal(false);\n        loadProject(); // Refresh project stats\n        loadImages(); // Refresh images\n    };\n    const handleExport = async ()=>{\n        if (!project) return;\n        try {\n            if (project.stats.classified_images === 0) {\n                alert('No classified images to export');\n                return;\n            }\n            await _services_api__WEBPACK_IMPORTED_MODULE_3__.apiService.exportProject(project.id);\n        } catch (error) {\n            console.error('Error exporting project:', error);\n            alert('Failed to export project');\n        }\n    };\n    const handleExportCSV = async ()=>{\n        if (!project) return;\n        try {\n            if (project.stats.classified_images === 0) {\n                alert('No classified images to export');\n                return;\n            }\n            await _services_api__WEBPACK_IMPORTED_MODULE_3__.apiService.exportProjectCSV(project.id);\n        } catch (error) {\n            console.error('Error exporting CSV:', error);\n            alert('Failed to export CSV');\n        }\n    };\n    if (isLoading && !project) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                lineNumber: 95,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n            lineNumber: 94,\n            columnNumber: 7\n        }, this);\n    }\n    if (error && !project) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg\",\n            children: error\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n            lineNumber: 102,\n            columnNumber: 7\n        }, this);\n    }\n    if (!project) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                    children: \"Project not found\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    href: \"/\",\n                    className: \"text-blue-600 hover:text-blue-700\",\n                    children: \"Back to Projects\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n            lineNumber: 110,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-start mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4 mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    className: \"text-blue-600 hover:text-blue-700\",\n                                    children: \"← Back to Projects\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                children: project.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 11\n                            }, this),\n                            project.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-4\",\n                                children: project.description\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Classes:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2\",\n                                        children: project.classes.map((className, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-block bg-blue-100 text-blue-800 text-sm px-3 py-1 rounded-full\",\n                                                children: className\n                                            }, index, false, {\n                                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowUploadModal(true),\n                                className: \"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors\",\n                                children: \"Upload Images\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleExport,\n                                disabled: project.stats.classified_images === 0,\n                                className: \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: \"Export ZIP\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleExportCSV,\n                                disabled: project.stats.classified_images === 0,\n                                className: \"bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: \"Export CSV\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: project.stats.total_images\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"Total Images\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-green-600\",\n                                children: project.stats.classified_images\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"Classified\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-orange-600\",\n                                children: project.stats.unclassified_images\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"Remaining\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-blue-600\",\n                                children: [\n                                    project.stats.progress_percentage,\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"Complete\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between text-sm text-gray-600 mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Classification Progress\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    project.stats.progress_percentage,\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full bg-gray-200 rounded-full h-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-600 h-3 rounded-full transition-all duration-300\",\n                            style: {\n                                width: \"\".concat(project.stats.progress_percentage, \"%\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                lineNumber: 195,\n                columnNumber: 7\n            }, this),\n            project.stats.total_images > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex space-x-4 mb-8\",\n                children: [\n                    project.stats.unclassified_images > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/projects/\".concat(project.id, \"/classify\"),\n                        className: \"bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-colors\",\n                        children: [\n                            \"Start Classifying (\",\n                            project.stats.unclassified_images,\n                            \" remaining)\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/projects/\".concat(project.id, \"/classify\"),\n                        className: \"bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors\",\n                        children: \"Review Classifications\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                lineNumber: 210,\n                columnNumber: 9\n            }, this),\n            isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center py-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                    lineNumber: 231,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                lineNumber: 230,\n                columnNumber: 9\n            }, this) : images.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-12 bg-gray-50 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-400 text-6xl mb-4\",\n                        children: \"\\uD83D\\uDCF7\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                        children: \"No images uploaded\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-6\",\n                        children: \"Upload a ZIP file containing images to start classifying\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setShowUploadModal(true),\n                        className: \"bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-colors\",\n                        children: \"Upload Images\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                lineNumber: 234,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4\",\n                children: images.map((image)=>{\n                    const imageUrl = _services_api__WEBPACK_IMPORTED_MODULE_3__.apiService.getImageUrl(image.file_path);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative group\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"aspect-square bg-gray-100 rounded-lg overflow-hidden border border-gray-300\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: imageUrl,\n                                    alt: image.original_filename,\n                                    className: \"w-full h-full object-cover\",\n                                    onError: (e)=>{\n                                        var _target_parentElement;\n                                        // Replace with error message\n                                        const target = e.currentTarget;\n                                        target.style.display = 'none';\n                                        const errorDiv = document.createElement('div');\n                                        errorDiv.className = 'w-full h-full flex items-center justify-center text-gray-500 text-xs p-2 text-center bg-gray-100';\n                                        errorDiv.innerHTML = '\\n                        <div>\\n                          <div class=\"mb-1 text-2xl\">\\uD83D\\uDCF7</div>\\n                          <div class=\"break-words\">'.concat(image.original_filename, '</div>\\n                          <div class=\"text-red-500 mt-1\">Failed to load</div>\\n                        </div>\\n                      ');\n                                        (_target_parentElement = target.parentElement) === null || _target_parentElement === void 0 ? void 0 : _target_parentElement.appendChild(errorDiv);\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-opacity rounded-lg flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"opacity-0 group-hover:opacity-100 text-white text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm font-medium truncate px-2\",\n                                            children: image.original_filename\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 21\n                                        }, this),\n                                        image.classification && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs bg-green-600 px-2 py-1 rounded mt-1\",\n                                            children: image.classification.class_name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 23\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 17\n                            }, this),\n                            image.status === 'classified' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-2 right-2 bg-green-600 text-white text-xs px-2 py-1 rounded\",\n                                children: \"✓\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 19\n                            }, this)\n                        ]\n                    }, image.id, true, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 15\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                lineNumber: 248,\n                columnNumber: 9\n            }, this),\n            showUploadModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UploadModal__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                projectId: project.id,\n                onClose: ()=>setShowUploadModal(false),\n                onUploadComplete: handleUploadComplete\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                lineNumber: 301,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n        lineNumber: 120,\n        columnNumber: 5\n    }, this);\n}\n_s(ProjectDetail, \"Xd+JudOL4NiHKtblGTVEOP84+Z4=\");\n_c = ProjectDetail;\nvar _c;\n$RefreshReg$(_c, \"ProjectDetail\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ProjectDetail.tsx\n"));

/***/ })

});