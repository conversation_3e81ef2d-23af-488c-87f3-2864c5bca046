"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/page",{

/***/ "(app-pages-browser)/./src/components/ProjectDetail.tsx":
/*!******************************************!*\
  !*** ./src/components/ProjectDetail.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProjectDetail)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/api */ \"(app-pages-browser)/./src/services/api.ts\");\n/* harmony import */ var _UploadModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./UploadModal */ \"(app-pages-browser)/./src/components/UploadModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction ProjectDetail(param) {\n    let { projectId } = param;\n    _s();\n    const [project, setProject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [images, setImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showUploadModal, setShowUploadModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProjectDetail.useEffect\": ()=>{\n            loadProject();\n            loadImages();\n        }\n    }[\"ProjectDetail.useEffect\"], [\n        projectId\n    ]);\n    const loadProject = async ()=>{\n        try {\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_3__.apiService.getProject(projectId);\n            if (response.success) {\n                setProject(response.data);\n            } else {\n                setError(response.error || 'Failed to load project');\n            }\n        } catch (err) {\n            setError('Failed to load project');\n            console.error('Error loading project:', err);\n        }\n    };\n    const loadImages = async ()=>{\n        try {\n            setIsLoading(true);\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_3__.apiService.getProjectImagesWithClassifications(projectId);\n            if (response.success) {\n                setImages(response.data || []);\n            } else {\n                setError(response.error || 'Failed to load images');\n            }\n        } catch (err) {\n            setError('Failed to load images');\n            console.error('Error loading images:', err);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleUploadComplete = ()=>{\n        setShowUploadModal(false);\n        loadProject(); // Refresh project stats\n        loadImages(); // Refresh images\n    };\n    const handleExport = async ()=>{\n        if (!project) return;\n        try {\n            if (project.stats.classified_images === 0) {\n                alert('No classified images to export');\n                return;\n            }\n            await _services_api__WEBPACK_IMPORTED_MODULE_3__.apiService.exportProject(project.id);\n        } catch (error) {\n            console.error('Error exporting project:', error);\n            alert('Failed to export project');\n        }\n    };\n    const handleExportCSV = async ()=>{\n        if (!project) return;\n        try {\n            if (project.stats.classified_images === 0) {\n                alert('No classified images to export');\n                return;\n            }\n            await _services_api__WEBPACK_IMPORTED_MODULE_3__.apiService.exportProjectCSV(project.id);\n        } catch (error) {\n            console.error('Error exporting CSV:', error);\n            alert('Failed to export CSV');\n        }\n    };\n    if (isLoading && !project) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                lineNumber: 95,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n            lineNumber: 94,\n            columnNumber: 7\n        }, this);\n    }\n    if (error && !project) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg\",\n            children: error\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n            lineNumber: 102,\n            columnNumber: 7\n        }, this);\n    }\n    if (!project) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                    children: \"Project not found\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    href: \"/\",\n                    className: \"text-blue-600 hover:text-blue-700\",\n                    children: \"Back to Projects\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n            lineNumber: 110,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-start mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4 mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    className: \"text-blue-600 hover:text-blue-700\",\n                                    children: \"← Back to Projects\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                children: project.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 11\n                            }, this),\n                            project.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-4\",\n                                children: project.description\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Classes:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2\",\n                                        children: project.classes.map((className, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-block bg-blue-100 text-blue-800 text-sm px-3 py-1 rounded-full\",\n                                                children: className\n                                            }, index, false, {\n                                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowUploadModal(true),\n                                className: \"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors\",\n                                children: \"Upload Images\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleExport,\n                                disabled: project.stats.classified_images === 0,\n                                className: \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: \"Export ZIP\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleExportCSV,\n                                disabled: project.stats.classified_images === 0,\n                                className: \"bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: \"Export CSV\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: project.stats.total_images\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"Total Images\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-green-600\",\n                                children: project.stats.classified_images\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"Classified\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-orange-600\",\n                                children: project.stats.unclassified_images\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"Remaining\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-blue-600\",\n                                children: [\n                                    project.stats.progress_percentage,\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"Complete\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between text-sm text-gray-600 mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Classification Progress\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    project.stats.progress_percentage,\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full bg-gray-200 rounded-full h-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-600 h-3 rounded-full transition-all duration-300\",\n                            style: {\n                                width: \"\".concat(project.stats.progress_percentage, \"%\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                lineNumber: 195,\n                columnNumber: 7\n            }, this),\n            project.stats.total_images > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex space-x-4 mb-8\",\n                children: [\n                    project.stats.unclassified_images > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/projects/\".concat(project.id, \"/classify\"),\n                        className: \"bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-colors\",\n                        children: [\n                            \"Start Classifying (\",\n                            project.stats.unclassified_images,\n                            \" remaining)\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/projects/\".concat(project.id, \"/classify\"),\n                        className: \"bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors\",\n                        children: \"Review Classifications\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                lineNumber: 210,\n                columnNumber: 9\n            }, this),\n            isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center py-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                    lineNumber: 231,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                lineNumber: 230,\n                columnNumber: 9\n            }, this) : images.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-12 bg-gray-50 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-400 text-6xl mb-4\",\n                        children: \"\\uD83D\\uDCF7\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                        children: \"No images uploaded\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-6\",\n                        children: \"Upload a ZIP file containing images to start classifying\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setShowUploadModal(true),\n                        className: \"bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-colors\",\n                        children: \"Upload Images\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                lineNumber: 234,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4\",\n                children: images.map((image)=>{\n                    const imageUrl = _services_api__WEBPACK_IMPORTED_MODULE_3__.apiService.getImageUrl(image.file_path);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative group\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"aspect-square bg-gray-200 rounded-lg overflow-hidden border border-gray-300\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: imageUrl,\n                                    alt: image.original_filename,\n                                    className: \"w-full h-full object-cover\",\n                                    loading: \"lazy\",\n                                    onError: (e)=>{\n                                        // Hide broken image and show filename instead\n                                        e.currentTarget.style.display = 'none';\n                                        const parent = e.currentTarget.parentElement;\n                                        if (parent) {\n                                            parent.innerHTML = '\\n                          <div class=\"w-full h-full flex items-center justify-center text-gray-500 text-xs p-2 text-center\">\\n                            <div>\\n                              <div class=\"mb-1\">\\uD83D\\uDCF7</div>\\n                              <div>'.concat(image.original_filename, \"</div>\\n                            </div>\\n                          </div>\\n                        \");\n                                        }\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-opacity rounded-lg flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"opacity-0 group-hover:opacity-100 text-white text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm font-medium truncate px-2\",\n                                            children: image.original_filename\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 21\n                                        }, this),\n                                        image.classification && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs bg-green-600 px-2 py-1 rounded mt-1\",\n                                            children: image.classification.class_name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 23\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 17\n                            }, this),\n                            image.status === 'classified' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-2 right-2 bg-green-600 text-white text-xs px-2 py-1 rounded\",\n                                children: \"✓\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 19\n                            }, this)\n                        ]\n                    }, image.id, true, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 15\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                lineNumber: 248,\n                columnNumber: 9\n            }, this),\n            showUploadModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UploadModal__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                projectId: project.id,\n                onClose: ()=>setShowUploadModal(false),\n                onUploadComplete: handleUploadComplete\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                lineNumber: 302,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n        lineNumber: 120,\n        columnNumber: 5\n    }, this);\n}\n_s(ProjectDetail, \"Xd+JudOL4NiHKtblGTVEOP84+Z4=\");\n_c = ProjectDetail;\nvar _c;\n$RefreshReg$(_c, \"ProjectDetail\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ProjectDetail.tsx\n"));

/***/ })

});