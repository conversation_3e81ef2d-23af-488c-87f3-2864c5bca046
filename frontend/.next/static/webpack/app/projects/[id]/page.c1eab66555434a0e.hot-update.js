"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/page",{

/***/ "(app-pages-browser)/./src/components/ProjectDetail.tsx":
/*!******************************************!*\
  !*** ./src/components/ProjectDetail.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProjectDetail)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/api */ \"(app-pages-browser)/./src/services/api.ts\");\n/* harmony import */ var _UploadModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./UploadModal */ \"(app-pages-browser)/./src/components/UploadModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction ProjectDetail(param) {\n    let { projectId } = param;\n    _s();\n    const [project, setProject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [images, setImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showUploadModal, setShowUploadModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProjectDetail.useEffect\": ()=>{\n            loadProject();\n            loadImages();\n        }\n    }[\"ProjectDetail.useEffect\"], [\n        projectId\n    ]);\n    const loadProject = async ()=>{\n        try {\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_3__.apiService.getProject(projectId);\n            if (response.success) {\n                setProject(response.data);\n            } else {\n                setError(response.error || 'Failed to load project');\n            }\n        } catch (err) {\n            setError('Failed to load project');\n            console.error('Error loading project:', err);\n        }\n    };\n    const loadImages = async ()=>{\n        try {\n            setIsLoading(true);\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_3__.apiService.getProjectImagesWithClassifications(projectId);\n            if (response.success) {\n                setImages(response.data || []);\n            } else {\n                setError(response.error || 'Failed to load images');\n            }\n        } catch (err) {\n            setError('Failed to load images');\n            console.error('Error loading images:', err);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleUploadComplete = ()=>{\n        setShowUploadModal(false);\n        loadProject(); // Refresh project stats\n        loadImages(); // Refresh images\n    };\n    const handleExport = async ()=>{\n        if (!project) return;\n        try {\n            if (project.stats.classified_images === 0) {\n                alert('No classified images to export');\n                return;\n            }\n            await _services_api__WEBPACK_IMPORTED_MODULE_3__.apiService.exportProject(project.id);\n        } catch (error) {\n            console.error('Error exporting project:', error);\n            alert('Failed to export project');\n        }\n    };\n    const handleExportCSV = async ()=>{\n        if (!project) return;\n        try {\n            if (project.stats.classified_images === 0) {\n                alert('No classified images to export');\n                return;\n            }\n            await _services_api__WEBPACK_IMPORTED_MODULE_3__.apiService.exportProjectCSV(project.id);\n        } catch (error) {\n            console.error('Error exporting CSV:', error);\n            alert('Failed to export CSV');\n        }\n    };\n    if (isLoading && !project) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                lineNumber: 95,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n            lineNumber: 94,\n            columnNumber: 7\n        }, this);\n    }\n    if (error && !project) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg\",\n            children: error\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n            lineNumber: 102,\n            columnNumber: 7\n        }, this);\n    }\n    if (!project) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                    children: \"Project not found\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    href: \"/\",\n                    className: \"text-blue-600 hover:text-blue-700\",\n                    children: \"Back to Projects\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n            lineNumber: 110,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-start mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4 mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    className: \"text-blue-600 hover:text-blue-700\",\n                                    children: \"← Back to Projects\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                children: project.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 11\n                            }, this),\n                            project.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-4\",\n                                children: project.description\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Classes:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2\",\n                                        children: project.classes.map((className, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-block bg-blue-100 text-blue-800 text-sm px-3 py-1 rounded-full\",\n                                                children: className\n                                            }, index, false, {\n                                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowUploadModal(true),\n                                className: \"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors\",\n                                children: \"Upload Images\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleExport,\n                                disabled: project.stats.classified_images === 0,\n                                className: \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: \"Export Results\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: project.stats.total_images\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"Total Images\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-green-600\",\n                                children: project.stats.classified_images\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"Classified\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-orange-600\",\n                                children: project.stats.unclassified_images\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"Remaining\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-blue-600\",\n                                children: [\n                                    project.stats.progress_percentage,\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"Complete\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                lineNumber: 168,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between text-sm text-gray-600 mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Classification Progress\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    project.stats.progress_percentage,\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full bg-gray-200 rounded-full h-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-600 h-3 rounded-full transition-all duration-300\",\n                            style: {\n                                width: \"\".concat(project.stats.progress_percentage, \"%\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                lineNumber: 188,\n                columnNumber: 7\n            }, this),\n            project.stats.total_images > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex space-x-4 mb-8\",\n                children: [\n                    project.stats.unclassified_images > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/projects/\".concat(project.id, \"/classify\"),\n                        className: \"bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-colors\",\n                        children: [\n                            \"Start Classifying (\",\n                            project.stats.unclassified_images,\n                            \" remaining)\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/projects/\".concat(project.id, \"/classify\"),\n                        className: \"bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors\",\n                        children: \"Review Classifications\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                lineNumber: 203,\n                columnNumber: 9\n            }, this),\n            isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center py-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                    lineNumber: 224,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                lineNumber: 223,\n                columnNumber: 9\n            }, this) : images.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-12 bg-gray-50 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-400 text-6xl mb-4\",\n                        children: \"\\uD83D\\uDCF7\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                        children: \"No images uploaded\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-6\",\n                        children: \"Upload a ZIP file containing images to start classifying\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setShowUploadModal(true),\n                        className: \"bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-colors\",\n                        children: \"Upload Images\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                lineNumber: 227,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4\",\n                children: images.map((image)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative group\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"aspect-square bg-gray-200 rounded-lg overflow-hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: _services_api__WEBPACK_IMPORTED_MODULE_3__.apiService.getImageUrl(image.file_path),\n                                    alt: image.original_filename,\n                                    className: \"w-full h-full object-cover\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-opacity rounded-lg flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"opacity-0 group-hover:opacity-100 text-white text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm font-medium truncate px-2\",\n                                            children: image.original_filename\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 19\n                                        }, this),\n                                        image.classification && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs bg-green-600 px-2 py-1 rounded mt-1\",\n                                            children: image.classification.class_name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 15\n                            }, this),\n                            image.status === 'classified' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-2 right-2 bg-green-600 text-white text-xs px-2 py-1 rounded\",\n                                children: \"✓\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, image.id, true, {\n                        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                lineNumber: 241,\n                columnNumber: 9\n            }, this),\n            showUploadModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UploadModal__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                projectId: project.id,\n                onClose: ()=>setShowUploadModal(false),\n                onUploadComplete: handleUploadComplete\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n                lineNumber: 275,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/classify-test/frontend/src/components/ProjectDetail.tsx\",\n        lineNumber: 120,\n        columnNumber: 5\n    }, this);\n}\n_s(ProjectDetail, \"Xd+JudOL4NiHKtblGTVEOP84+Z4=\");\n_c = ProjectDetail;\nvar _c;\n$RefreshReg$(_c, \"ProjectDetail\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ProjectDetail.tsx\n"));

/***/ })

});