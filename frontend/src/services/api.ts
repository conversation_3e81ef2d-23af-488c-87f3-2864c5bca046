import { 
  Project, 
  CreateProjectRequest, 
  Image, 
  ImageWithClassification, 
  ClassifyImageRequest, 
  ApiResponse, 
  UploadResult 
} from '@/types';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api';

class ApiService {
  private async request<T>(
    endpoint: string, 
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${API_BASE_URL}${endpoint}`;
    
    try {
      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        ...options,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  // Project endpoints
  async getProjects(): Promise<ApiResponse<Project[]>> {
    return this.request<Project[]>('/projects');
  }

  async getProject(id: string): Promise<ApiResponse<Project>> {
    return this.request<Project>(`/projects/${id}`);
  }

  async createProject(data: CreateProjectRequest): Promise<ApiResponse<Project>> {
    return this.request<Project>('/projects', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updateProject(id: string, data: Partial<CreateProjectRequest>): Promise<ApiResponse<Project>> {
    return this.request<Project>(`/projects/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async deleteProject(id: string): Promise<ApiResponse<void>> {
    return this.request<void>(`/projects/${id}`, {
      method: 'DELETE',
    });
  }

  // Upload endpoint
  async uploadZip(projectId: string, file: File): Promise<ApiResponse<UploadResult>> {
    const formData = new FormData();
    formData.append('zipFile', file);

    try {
      const response = await fetch(`${API_BASE_URL}/projects/${projectId}/upload`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Upload failed:', error);
      throw error;
    }
  }

  // Image endpoints
  async getProjectImages(projectId: string, status?: 'classified' | 'unclassified'): Promise<ApiResponse<Image[]>> {
    const params = status ? `?status=${status}` : '';
    return this.request<Image[]>(`/images/project/${projectId}${params}`);
  }

  async getProjectImagesWithClassifications(projectId: string): Promise<ApiResponse<ImageWithClassification[]>> {
    return this.request<ImageWithClassification[]>(`/images/project/${projectId}/with-classifications`);
  }

  async getNextUnclassifiedImage(projectId: string, currentImageId?: string): Promise<ApiResponse<Image | null>> {
    const params = currentImageId ? `?currentImageId=${currentImageId}` : '';
    return this.request<Image | null>(`/images/project/${projectId}/next-unclassified${params}`);
  }

  // Classification endpoints
  async classifyImage(data: ClassifyImageRequest): Promise<ApiResponse<{ id: string; image_id: string; project_id: string; class_name: string; classified_at: string }>> {
    return this.request('/classifications', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async removeClassification(imageId: string): Promise<ApiResponse<void>> {
    return this.request<void>(`/classifications/image/${imageId}`, {
      method: 'DELETE',
    });
  }

  async getClassificationStats(projectId: string): Promise<ApiResponse<{ [className: string]: number }>> {
    return this.request(`/classifications/project/${projectId}/stats`);
  }

  // Export endpoint
  async exportProject(projectId: string): Promise<void> {
    try {
      const response = await fetch(`${API_BASE_URL}/projects/${projectId}/export`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // Get filename from Content-Disposition header or use default
      const contentDisposition = response.headers.get('Content-Disposition');
      let filename = 'export.zip';
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+)"/);
        if (filenameMatch) {
          filename = filenameMatch[1];
        }
      }

      // Create blob and download
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Export failed:', error);
      throw error;
    }
  }

  // Export CSV endpoint
  async exportProjectCSV(projectId: string): Promise<void> {
    try {
      const response = await fetch(`${API_BASE_URL}/projects/${projectId}/export-csv`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // Get filename from Content-Disposition header or use default
      const contentDisposition = response.headers.get('Content-Disposition');
      let filename = 'classifications.csv';
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+)"/);
        if (filenameMatch) {
          filename = filenameMatch[1];
        }
      }

      // Create blob and download
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('CSV export failed:', error);
      throw error;
    }
  }

  // Utility method to get image URL
  getImageUrl(imagePath: string): string {
    return `${API_BASE_URL.replace('/api', '')}/uploads/${imagePath}`;
  }
}

export const apiService = new ApiService();
