// Project types
export interface Project {
  id: string;
  name: string;
  description?: string;
  classes: string[];
  created_at: string;
  updated_at: string;
  stats: ProjectStats;
}

export interface ProjectStats {
  total_images: number;
  classified_images: number;
  unclassified_images: number;
  progress_percentage: number;
}

export interface CreateProjectRequest {
  name: string;
  description?: string;
  classes: string[];
}

// Image types
export interface Image {
  id: string;
  project_id: string;
  filename: string;
  original_filename: string;
  file_path: string;
  file_size: number;
  mime_type: string;
  width?: number;
  height?: number;
  status: 'unclassified' | 'classified';
  created_at: string;
}

// Classification types
export interface Classification {
  id: string;
  image_id: string;
  project_id: string;
  class_name: string;
  classified_at: string;
}

export interface ImageWithClassification extends Image {
  classification?: Classification;
}

export interface ClassifyImageRequest {
  image_id: string;
  class_name: string;
}

// API Response types
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Upload types
export interface UploadResult {
  total_files: number;
  processed_files: number;
  skipped_files: number;
  errors: string[];
}

// UI State types
export interface ClassificationState {
  currentImage: ImageWithClassification | null;
  images: ImageWithClassification[];
  currentIndex: number;
  isLoading: boolean;
  error: string | null;
}
