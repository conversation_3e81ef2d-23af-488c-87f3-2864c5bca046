'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Project, ImageWithClassification, ClassificationState } from '@/types';
import { apiService } from '@/services/api';

interface ClassificationInterfaceProps {
  projectId: string;
}

export default function ClassificationInterface({ projectId }: ClassificationInterfaceProps) {
  const [project, setProject] = useState<Project | null>(null);
  const [state, setState] = useState<ClassificationState>({
    currentImage: null,
    images: [],
    currentIndex: 0,
    isLoading: true,
    error: null
  });

  useEffect(() => {
    loadProject();
    loadImages();
  }, [projectId]);

  const loadProject = async () => {
    try {
      const response = await apiService.getProject(projectId);
      if (response.success) {
        setProject(response.data);
      } else {
        setState(prev => ({ ...prev, error: response.error || 'Failed to load project' }));
      }
    } catch (err) {
      setState(prev => ({ ...prev, error: 'Failed to load project' }));
      console.error('Error loading project:', err);
    }
  };

  const loadImages = async () => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));
      const response = await apiService.getProjectImagesWithClassifications(projectId);
      
      if (response.success && response.data) {
        const images = response.data;
        setState(prev => ({
          ...prev,
          images,
          currentImage: images.length > 0 ? images[0] : null,
          currentIndex: 0,
          isLoading: false
        }));
      } else {
        setState(prev => ({ 
          ...prev, 
          error: response.error || 'Failed to load images',
          isLoading: false 
        }));
      }
    } catch (err) {
      setState(prev => ({ 
        ...prev, 
        error: 'Failed to load images',
        isLoading: false 
      }));
      console.error('Error loading images:', err);
    }
  };

  const classifyImage = async (className: string) => {
    if (!state.currentImage) return;

    // Check if image is already classified with the same class
    if (state.currentImage.classification?.class_name === className) {
      return; // Already classified with this class, do nothing
    }

    try {
      setState(prev => ({ ...prev, error: null })); // Clear any previous errors

      const response = await apiService.classifyImage({
        image_id: state.currentImage.id,
        class_name: className
      });

      if (response.success) {
        // Update the current image with classification
        const updatedImage: ImageWithClassification = {
          ...state.currentImage,
          status: 'classified',
          classification: {
            id: response.data.id,
            image_id: state.currentImage.id,
            project_id: projectId,
            class_name: className,
            classified_at: new Date().toISOString()
          }
        };

        // Update images array
        const updatedImages = state.images.map(img =>
          img.id === state.currentImage!.id ? updatedImage : img
        );

        setState(prev => ({
          ...prev,
          images: updatedImages,
          currentImage: updatedImage,
          error: null
        }));

        // Auto-advance to next unclassified image after a short delay
        setTimeout(() => {
          goToNextUnclassified();
        }, 300);

        // Refresh project stats
        loadProject();
      } else {
        setState(prev => ({ ...prev, error: response.error || 'Failed to classify image' }));
      }
    } catch (err) {
      setState(prev => ({ ...prev, error: 'Failed to classify image' }));
      console.error('Error classifying image:', err);
    }
  };

  const removeClassification = async () => {
    if (!state.currentImage || !state.currentImage.classification) return;

    try {
      const response = await apiService.removeClassification(state.currentImage.id);

      if (response.success) {
        // Update the current image to remove classification
        const updatedImage: ImageWithClassification = {
          ...state.currentImage,
          status: 'unclassified',
          classification: undefined
        };

        // Update images array
        const updatedImages = state.images.map(img => 
          img.id === state.currentImage!.id ? updatedImage : img
        );

        setState(prev => ({
          ...prev,
          images: updatedImages,
          currentImage: updatedImage
        }));

        // Refresh project stats
        loadProject();
      } else {
        setState(prev => ({ ...prev, error: response.error || 'Failed to remove classification' }));
      }
    } catch (err) {
      setState(prev => ({ ...prev, error: 'Failed to remove classification' }));
      console.error('Error removing classification:', err);
    }
  };

  const goToImage = (index: number) => {
    if (index >= 0 && index < state.images.length) {
      setState(prev => ({
        ...prev,
        currentIndex: index,
        currentImage: prev.images[index]
      }));
    }
  };

  const goToNextUnclassified = () => {
    const nextUnclassified = state.images.findIndex((img, index) => 
      index > state.currentIndex && img.status === 'unclassified'
    );
    
    if (nextUnclassified !== -1) {
      goToImage(nextUnclassified);
    } else {
      // Look from the beginning
      const firstUnclassified = state.images.findIndex(img => img.status === 'unclassified');
      if (firstUnclassified !== -1) {
        goToImage(firstUnclassified);
      }
    }
  };

  const goToPrevious = () => {
    if (state.currentIndex > 0) {
      goToImage(state.currentIndex - 1);
    }
  };

  const goToNext = () => {
    if (state.currentIndex < state.images.length - 1) {
      goToImage(state.currentIndex + 1);
    }
  };

  // Keyboard navigation
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (!project) return;

      if (e.key >= '1' && e.key <= '9') {
        const classIndex = parseInt(e.key) - 1;
        if (classIndex < project.classes.length) {
          classifyImage(project.classes[classIndex]);
        }
      } else if (e.key === 'ArrowLeft') {
        goToPrevious();
      } else if (e.key === 'ArrowRight') {
        goToNext();
      } else if (e.key === 'Backspace' || e.key === 'Delete') {
        removeClassification();
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [project, state.currentIndex, state.currentImage]);

  if (state.isLoading) {
    return (
      <div className="flex items-center justify-center min-h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (state.error) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
        {state.error}
      </div>
    );
  }

  if (!project) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg font-medium text-gray-900 mb-2">Project not found</h3>
        <Link href="/" className="text-blue-600 hover:text-blue-700">
          Back to Projects
        </Link>
      </div>
    );
  }

  if (state.images.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 text-6xl mb-4">📷</div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">No images to classify</h3>
        <p className="text-gray-600 mb-6">
          Upload images to this project to start classifying
        </p>
        <Link
          href={`/projects/${projectId}`}
          className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
        >
          Back to Project
        </Link>
      </div>
    );
  }

  const unclassifiedCount = state.images.filter(img => img.status === 'unclassified').length;
  const progressPercentage = Math.round(((state.images.length - unclassifiedCount) / state.images.length) * 100);

  return (
    <div className="max-w-6xl mx-auto">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <Link
            href={`/projects/${projectId}`}
            className="text-blue-600 hover:text-blue-700 mb-2 inline-block"
          >
            ← Back to {project.name}
          </Link>
          <h1 className="text-2xl font-bold text-gray-900">Classification Interface</h1>
        </div>
        <div className="text-right">
          <div className="text-sm text-gray-600">
            Image {state.currentIndex + 1} of {state.images.length}
          </div>
          <div className="text-sm text-gray-600">
            {unclassifiedCount} remaining • {progressPercentage}% complete
          </div>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="mb-6">
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-green-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${progressPercentage}%` }}
          ></div>
        </div>
      </div>

      {/* Error Display */}
      {state.error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6">
          {state.error}
          <button
            onClick={() => setState(prev => ({ ...prev, error: null }))}
            className="ml-2 text-red-500 hover:text-red-700"
          >
            ×
          </button>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Image Display */}
        <div className="lg:col-span-2">
          {state.currentImage && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden mb-4">
                <img
                  src={apiService.getImageUrl(state.currentImage.file_path)}
                  alt={state.currentImage.original_filename}
                  className="w-full h-full object-contain"
                />
              </div>
              
              <div className="flex justify-between items-center mb-4">
                <div>
                  <h3 className="font-medium text-gray-900">{state.currentImage.original_filename}</h3>
                  {state.currentImage.classification && (
                    <div className="text-sm text-green-600 mt-1">
                      Classified as: <span className="font-medium">{state.currentImage.classification.class_name}</span>
                    </div>
                  )}
                </div>
                
              </div>

              {/* Navigation */}
              <div className="flex justify-between items-center">
                <button
                  onClick={goToPrevious}
                  disabled={state.currentIndex === 0}
                  className="bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  ← Previous
                </button>
                
                <button
                  onClick={goToNextUnclassified}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
                >
                  Next Unclassified
                </button>
                
                <button
                  onClick={goToNext}
                  disabled={state.currentIndex === state.images.length - 1}
                  className="bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Next →
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Classification Panel */}
        <div className="space-y-6">
          {/* Class Buttons */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Classify Image</h3>
            <div className="space-y-2">
              {project.classes.map((className, index) => (
                <button
                  key={className}
                  onClick={() => classifyImage(className)}
                  className={`w-full text-left px-4 py-3 rounded-lg font-medium transition-colors ${
                    state.currentImage?.classification?.class_name === className
                      ? 'bg-green-600 text-white'
                      : 'bg-gray-100 hover:bg-gray-200 text-gray-900'
                  }`}
                >
                  <span className="text-sm text-gray-500 mr-2">({index + 1})</span>
                  {className}
                </button>
              ))}
            </div>
            <div className="mt-4 text-xs text-gray-500">
              Use keyboard shortcuts: 1-{project.classes.length} to classify, ← → to navigate, Backspace to remove
            </div>
          </div>

          {/* Image Thumbnails */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">All Images</h3>
            <div className="grid grid-cols-4 gap-2 max-h-96 overflow-y-auto">
              {state.images.map((image, index) => (
                <button
                  key={image.id}
                  onClick={() => goToImage(index)}
                  className={`relative aspect-square rounded overflow-hidden border-2 transition-colors ${
                    index === state.currentIndex
                      ? 'border-blue-500'
                      : image.status === 'classified'
                      ? 'border-green-500'
                      : 'border-gray-200'
                  }`}
                >
                  <img
                    src={apiService.getImageUrl(image.file_path)}
                    alt={image.original_filename}
                    className="w-full h-full object-cover"
                  />
                  {image.status === 'classified' && (
                    <div className="absolute top-1 right-1">
                      <div className="bg-green-600 text-white text-xs px-1 py-0.5 rounded">✓</div>
                    </div>
                  )}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
