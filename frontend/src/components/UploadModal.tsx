'use client';

import { useState, useRef } from 'react';
import { UploadResult } from '@/types';
import { apiService } from '@/services/api';

interface UploadModalProps {
  projectId: string;
  onClose: () => void;
  onUploadComplete: () => void;
}

export default function UploadModal({ projectId, onClose, onUploadComplete }: UploadModalProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadResult, setUploadResult] = useState<UploadResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [dragOver, setDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (file: File) => {
    if (!file.name.toLowerCase().endsWith('.zip')) {
      setError('Please select a ZIP file');
      return;
    }

    if (file.size > 100 * 1024 * 1024) { // 100MB limit
      setError('File size must be less than 100MB');
      return;
    }

    uploadFile(file);
  };

  const uploadFile = async (file: File) => {
    try {
      setIsUploading(true);
      setError(null);
      setUploadResult(null);

      const response = await apiService.uploadZip(projectId, file);
      
      if (response.success && response.data) {
        setUploadResult(response.data);
        if (response.data.processed_files > 0) {
          // Auto-close after successful upload
          setTimeout(() => {
            onUploadComplete();
          }, 2000);
        }
      } else {
        setError(response.error || 'Upload failed');
      }
    } catch (err) {
      setError('Upload failed');
      console.error('Upload error:', err);
    } finally {
      setIsUploading(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold text-gray-900">Upload Images</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {!isUploading && !uploadResult && (
          <>
            <div
              className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                dragOver
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-300 hover:border-gray-400'
              }`}
              onDrop={handleDrop}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
            >
              <div className="text-gray-400 text-4xl mb-4">📁</div>
              <p className="text-gray-600 mb-4">
                Drag and drop a ZIP file here, or click to select
              </p>
              <button
                onClick={() => fileInputRef.current?.click()}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
              >
                Select ZIP File
              </button>
              <input
                ref={fileInputRef}
                type="file"
                accept=".zip"
                onChange={handleFileInputChange}
                className="hidden"
              />
            </div>

            <div className="mt-4 text-sm text-gray-600">
              <p className="mb-2">Requirements:</p>
              <ul className="list-disc list-inside space-y-1">
                <li>ZIP file containing images (JPG, PNG, GIF, BMP, WebP)</li>
                <li>Maximum file size: 100MB</li>
                <li>Images will be extracted and processed automatically</li>
              </ul>
            </div>
          </>
        )}

        {isUploading && (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Uploading and processing images...</p>
          </div>
        )}

        {uploadResult && (
          <div className="py-4">
            <div className="text-center mb-4">
              <div className="text-green-600 text-4xl mb-2">✅</div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Upload Complete!</h3>
            </div>
            
            <div className="bg-gray-50 rounded-lg p-4 space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-600">Total files:</span>
                <span className="font-medium">{uploadResult.total_files}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Processed:</span>
                <span className="font-medium text-green-600">{uploadResult.processed_files}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Skipped:</span>
                <span className="font-medium text-orange-600">{uploadResult.skipped_files}</span>
              </div>
              {uploadResult.errors.length > 0 && (
                <div className="mt-4">
                  <p className="text-red-600 font-medium mb-2">Errors:</p>
                  <div className="text-sm text-red-600 space-y-1">
                    {uploadResult.errors.map((error, index) => (
                      <div key={index}>{error}</div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            <div className="mt-4 text-center">
              <button
                onClick={onUploadComplete}
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors"
              >
                Continue
              </button>
            </div>
          </div>
        )}

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mt-4">
            {error}
          </div>
        )}
      </div>
    </div>
  );
}
