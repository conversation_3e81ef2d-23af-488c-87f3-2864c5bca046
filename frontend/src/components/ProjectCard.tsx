'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Project } from '@/types';
import { apiService } from '@/services/api';

interface ProjectCardProps {
  project: Project;
  onDeleted: () => void;
  onRefresh: () => void;
}

export default function ProjectCard({ project, onDeleted, onRefresh }: ProjectCardProps) {
  const [isDeleting, setIsDeleting] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  const handleDelete = async () => {
    try {
      setIsDeleting(true);
      const response = await apiService.deleteProject(project.id);
      if (response.success) {
        onDeleted();
      } else {
        alert('Failed to delete project: ' + response.error);
      }
    } catch (error) {
      console.error('Error deleting project:', error);
      alert('Failed to delete project');
    } finally {
      setIsDeleting(false);
      setShowDeleteConfirm(false);
    }
  };

  const handleExport = async () => {
    try {
      if (project.stats.classified_images === 0) {
        alert('No classified images to export');
        return;
      }
      await apiService.exportProject(project.id);
    } catch (error) {
      console.error('Error exporting project:', error);
      alert('Failed to export project');
    }
  };

  const progressPercentage = project.stats.progress_percentage;

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
      <div className="flex justify-between items-start mb-4">
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-gray-900 mb-1">
            {project.name}
          </h3>
          {project.description && (
            <p className="text-gray-600 text-sm mb-3">{project.description}</p>
          )}
        </div>
        <div className="flex space-x-2">
          <button
            onClick={handleExport}
            disabled={project.stats.classified_images === 0}
            className="text-green-600 hover:text-green-700 disabled:text-gray-400 disabled:cursor-not-allowed"
            title="Export classified images"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </button>
          <button
            onClick={() => setShowDeleteConfirm(true)}
            className="text-red-600 hover:text-red-700"
            title="Delete project"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
          </button>
        </div>
      </div>

      <div className="mb-4">
        <div className="flex justify-between text-sm text-gray-600 mb-2">
          <span>Progress</span>
          <span>{progressPercentage}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${progressPercentage}%` }}
          ></div>
        </div>
      </div>

      <div className="grid grid-cols-3 gap-4 mb-4 text-center">
        <div>
          <div className="text-lg font-semibold text-gray-900">
            {project.stats.total_images}
          </div>
          <div className="text-xs text-gray-600">Total</div>
        </div>
        <div>
          <div className="text-lg font-semibold text-green-600">
            {project.stats.classified_images}
          </div>
          <div className="text-xs text-gray-600">Classified</div>
        </div>
        <div>
          <div className="text-lg font-semibold text-orange-600">
            {project.stats.unclassified_images}
          </div>
          <div className="text-xs text-gray-600">Remaining</div>
        </div>
      </div>

      <div className="mb-4">
        <div className="text-sm text-gray-600 mb-2">Classes:</div>
        <div className="flex flex-wrap gap-1">
          {project.classes.map((className, index) => (
            <span
              key={index}
              className="inline-block bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded"
            >
              {className}
            </span>
          ))}
        </div>
      </div>

      <div className="flex space-x-2">
        <Link
          href={`/projects/${project.id}`}
          className="flex-1 bg-blue-600 hover:bg-blue-700 text-white text-center py-2 px-4 rounded-lg font-medium transition-colors"
        >
          Open Project
        </Link>
        {project.stats.unclassified_images > 0 && (
          <Link
            href={`/projects/${project.id}/classify`}
            className="flex-1 bg-green-600 hover:bg-green-700 text-white text-center py-2 px-4 rounded-lg font-medium transition-colors"
          >
            Classify
          </Link>
        )}
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-sm mx-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Delete Project
            </h3>
            <p className="text-gray-600 mb-4">
              Are you sure you want to delete "{project.name}"? This action cannot be undone.
            </p>
            <div className="flex space-x-3">
              <button
                onClick={() => setShowDeleteConfirm(false)}
                className="flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800 py-2 px-4 rounded-lg font-medium transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleDelete}
                disabled={isDeleting}
                className="flex-1 bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-lg font-medium transition-colors disabled:opacity-50"
              >
                {isDeleting ? 'Deleting...' : 'Delete'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
