'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Project, ImageWithClassification } from '@/types';
import { apiService } from '@/services/api';
import UploadModal from './UploadModal';

interface ProjectDetailProps {
  projectId: string;
}

export default function ProjectDetail({ projectId }: ProjectDetailProps) {
  const [project, setProject] = useState<Project | null>(null);
  const [images, setImages] = useState<ImageWithClassification[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showUploadModal, setShowUploadModal] = useState(false);

  useEffect(() => {
    loadProject();
    loadImages();
  }, [projectId]);

  const loadProject = async () => {
    try {
      const response = await apiService.getProject(projectId);
      if (response.success) {
        setProject(response.data);
      } else {
        setError(response.error || 'Failed to load project');
      }
    } catch (err) {
      setError('Failed to load project');
      console.error('Error loading project:', err);
    }
  };

  const loadImages = async () => {
    try {
      setIsLoading(true);
      const response = await apiService.getProjectImagesWithClassifications(projectId);
      if (response.success) {
        setImages(response.data || []);
      } else {
        setError(response.error || 'Failed to load images');
      }
    } catch (err) {
      setError('Failed to load images');
      console.error('Error loading images:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleUploadComplete = () => {
    setShowUploadModal(false);
    loadProject(); // Refresh project stats
    loadImages(); // Refresh images
  };

  const handleExport = async () => {
    if (!project) return;

    try {
      if (project.stats.classified_images === 0) {
        alert('No classified images to export');
        return;
      }
      await apiService.exportProject(project.id);
    } catch (error) {
      console.error('Error exporting project:', error);
      alert('Failed to export project');
    }
  };

  const handleExportCSV = async () => {
    if (!project) return;

    try {
      if (project.stats.classified_images === 0) {
        alert('No classified images to export');
        return;
      }
      await apiService.exportProjectCSV(project.id);
    } catch (error) {
      console.error('Error exporting CSV:', error);
      alert('Failed to export CSV');
    }
  };

  if (isLoading && !project) {
    return (
      <div className="flex items-center justify-center min-h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error && !project) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
        {error}
      </div>
    );
  }

  if (!project) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg font-medium text-gray-900 mb-2">Project not found</h3>
        <Link href="/" className="text-blue-600 hover:text-blue-700">
          Back to Projects
        </Link>
      </div>
    );
  }

  return (
    <div>
      {/* Header */}
      <div className="flex justify-between items-start mb-8">
        <div>
          <div className="flex items-center space-x-4 mb-4">
            <Link href="/" className="text-blue-600 hover:text-blue-700">
              ← Back to Projects
            </Link>
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">{project.name}</h1>
          {project.description && (
            <p className="text-gray-600 mb-4">{project.description}</p>
          )}
          
          {/* Classes */}
          <div className="mb-4">
            <h3 className="text-sm font-medium text-gray-700 mb-2">Classes:</h3>
            <div className="flex flex-wrap gap-2">
              {project.classes.map((className, index) => (
                <span
                  key={index}
                  className="inline-block bg-blue-100 text-blue-800 text-sm px-3 py-1 rounded-full"
                >
                  {className}
                </span>
              ))}
            </div>
          </div>
        </div>

        <div className="flex space-x-3">
          <button
            onClick={() => setShowUploadModal(true)}
            className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
          >
            Upload Images
          </button>
          <button
            onClick={handleExport}
            disabled={project.stats.classified_images === 0}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Export ZIP
          </button>
          <button
            onClick={handleExportCSV}
            disabled={project.stats.classified_images === 0}
            className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Export CSV
          </button>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="text-2xl font-bold text-gray-900">{project.stats.total_images}</div>
          <div className="text-sm text-gray-600">Total Images</div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="text-2xl font-bold text-green-600">{project.stats.classified_images}</div>
          <div className="text-sm text-gray-600">Classified</div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="text-2xl font-bold text-orange-600">{project.stats.unclassified_images}</div>
          <div className="text-sm text-gray-600">Remaining</div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="text-2xl font-bold text-blue-600">{project.stats.progress_percentage}%</div>
          <div className="text-sm text-gray-600">Complete</div>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="mb-8">
        <div className="flex justify-between text-sm text-gray-600 mb-2">
          <span>Classification Progress</span>
          <span>{project.stats.progress_percentage}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-3">
          <div
            className="bg-blue-600 h-3 rounded-full transition-all duration-300"
            style={{ width: `${project.stats.progress_percentage}%` }}
          ></div>
        </div>
      </div>

      {/* Action Buttons */}
      {project.stats.total_images > 0 && (
        <div className="flex space-x-4 mb-8">
          {project.stats.unclassified_images > 0 && (
            <Link
              href={`/projects/${project.id}/classify`}
              className="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
            >
              Start Classifying ({project.stats.unclassified_images} remaining)
            </Link>
          )}
          <Link
            href={`/projects/${project.id}/classify`}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
          >
            Review Classifications
          </Link>
        </div>
      )}

      {/* Images Grid */}
      {isLoading ? (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      ) : images.length === 0 ? (
        <div className="text-center py-12 bg-gray-50 rounded-lg">
          <div className="text-gray-400 text-6xl mb-4">📷</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No images uploaded</h3>
          <p className="text-gray-600 mb-6">
            Upload a ZIP file containing images to start classifying
          </p>
          <button
            onClick={() => setShowUploadModal(true)}
            className="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
          >
            Upload Images
          </button>
        </div>
      ) : (
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
          {images.map((image) => (
            <div key={image.id} className="relative group">
              <div className="aspect-square bg-gray-200 rounded-lg overflow-hidden">
                <img
                  src={apiService.getImageUrl(image.file_path)}
                  alt={image.original_filename}
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-opacity rounded-lg flex items-center justify-center">
                <div className="opacity-0 group-hover:opacity-100 text-white text-center">
                  <div className="text-sm font-medium truncate px-2">
                    {image.original_filename}
                  </div>
                  {image.classification && (
                    <div className="text-xs bg-green-600 px-2 py-1 rounded mt-1">
                      {image.classification.class_name}
                    </div>
                  )}
                </div>
              </div>
              {image.status === 'classified' && (
                <div className="absolute top-2 right-2 bg-green-600 text-white text-xs px-2 py-1 rounded">
                  ✓
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Upload Modal */}
      {showUploadModal && (
        <UploadModal
          projectId={project.id}
          onClose={() => setShowUploadModal(false)}
          onUploadComplete={handleUploadComplete}
        />
      )}
    </div>
  );
}
