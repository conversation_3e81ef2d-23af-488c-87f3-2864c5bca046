const express = require('express');
const cors = require('cors');
const path = require('path');
const sqlite3 = require('sqlite3');
const fs = require('fs');

const app = express();
const PORT = process.env.PORT || 3001;

// Initialize database
const dbPath = path.join(__dirname, '../data/classify.db');
const dataDir = path.dirname(dbPath);
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true });
}

const db = new sqlite3.Database(dbPath);

// Create tables
db.serialize(() => {
  // Projects table
  db.run(`
    CREATE TABLE IF NOT EXISTS projects (
      id TEXT PRIMARY KEY,
      name TEXT UNIQUE NOT NULL,
      description TEXT,
      classes TEXT NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Images table
  db.run(`
    CREATE TABLE IF NOT EXISTS images (
      id TEXT PRIMARY KEY,
      project_id TEXT NOT NULL,
      filename TEXT NOT NULL,
      original_filename TEXT NOT NULL,
      file_path TEXT NOT NULL,
      file_size INTEGER,
      mime_type TEXT,
      width INTEGER,
      height INTEGER,
      status TEXT DEFAULT 'unclassified',
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE CASCADE
    )
  `);

  // Classifications table
  db.run(`
    CREATE TABLE IF NOT EXISTS classifications (
      id TEXT PRIMARY KEY,
      image_id TEXT NOT NULL,
      project_id TEXT NOT NULL,
      class_name TEXT NOT NULL,
      classified_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (image_id) REFERENCES images (id) ON DELETE CASCADE,
      FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE CASCADE,
      UNIQUE(image_id)
    )
  `);
});

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Serve static files (uploaded images)
app.use('/uploads', express.static(path.join(__dirname, '../uploads')));

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Projects endpoints
app.get('/api/projects', (req, res) => {
  db.all('SELECT * FROM projects ORDER BY created_at DESC', (err, rows) => {
    if (err) {
      console.error('Database error:', err);
      return res.status(500).json({ success: false, error: 'Database error' });
    }

    const projects = rows.map(row => ({
      ...row,
      classes: JSON.parse(row.classes),
      stats: {
        total_images: 0,
        classified_images: 0,
        unclassified_images: 0,
        progress_percentage: 0
      }
    }));

    res.json({ success: true, data: projects });
  });
});

app.post('/api/projects', (req, res) => {
  const { name, description, classes } = req.body;

  if (!name || !classes || !Array.isArray(classes) || classes.length === 0) {
    return res.status(400).json({
      success: false,
      error: 'Name and classes are required'
    });
  }

  const id = require('crypto').randomUUID();
  const classesJson = JSON.stringify(classes);

  db.run(
    'INSERT INTO projects (id, name, description, classes) VALUES (?, ?, ?, ?)',
    [id, name, description || null, classesJson],
    function(err) {
      if (err) {
        console.error('Database error:', err);
        if (err.code === 'SQLITE_CONSTRAINT_UNIQUE') {
          return res.status(400).json({
            success: false,
            error: 'Project name already exists'
          });
        }
        return res.status(500).json({ success: false, error: 'Database error' });
      }

      // Return the created project
      db.get('SELECT * FROM projects WHERE id = ?', [id], (err, row) => {
        if (err) {
          console.error('Database error:', err);
          return res.status(500).json({ success: false, error: 'Database error' });
        }

        const project = {
          ...row,
          classes: JSON.parse(row.classes),
          stats: {
            total_images: 0,
            classified_images: 0,
            unclassified_images: 0,
            progress_percentage: 0
          }
        };

        res.status(201).json({
          success: true,
          data: project,
          message: 'Project created successfully'
        });
      });
    }
  );
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(500).json({
    error: 'Internal server error',
    message: err.message
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ error: 'Route not found' });
});

// Start server
app.listen(PORT, () => {
  console.log(`Server running on http://localhost:${PORT}`);
  console.log('Database initialized successfully');
});
