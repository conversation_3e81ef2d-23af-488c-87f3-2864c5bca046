const express = require('express');
const cors = require('cors');
const path = require('path');
const sqlite3 = require('sqlite3');
const fs = require('fs');
const multer = require('multer');
const unzipper = require('unzipper');
const sharp = require('sharp');

const app = express();
const PORT = process.env.PORT || 3001;

// Initialize database
const dbPath = path.join(__dirname, '../data/classify.db');
const dataDir = path.dirname(dbPath);
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true });
}

const db = new sqlite3.Database(dbPath);

// Create tables
db.serialize(() => {
  // Projects table
  db.run(`
    CREATE TABLE IF NOT EXISTS projects (
      id TEXT PRIMARY KEY,
      name TEXT UNIQUE NOT NULL,
      description TEXT,
      classes TEXT NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Images table
  db.run(`
    CREATE TABLE IF NOT EXISTS images (
      id TEXT PRIMARY KEY,
      project_id TEXT NOT NULL,
      filename TEXT NOT NULL,
      original_filename TEXT NOT NULL,
      file_path TEXT NOT NULL,
      file_size INTEGER,
      mime_type TEXT,
      width INTEGER,
      height INTEGER,
      status TEXT DEFAULT 'unclassified',
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE CASCADE
    )
  `);

  // Classifications table
  db.run(`
    CREATE TABLE IF NOT EXISTS classifications (
      id TEXT PRIMARY KEY,
      image_id TEXT NOT NULL,
      project_id TEXT NOT NULL,
      class_name TEXT NOT NULL,
      classified_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (image_id) REFERENCES images (id) ON DELETE CASCADE,
      FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE CASCADE,
      UNIQUE(image_id)
    )
  `);
});

// Ensure upload directories exist
const uploadsDir = path.join(__dirname, '../uploads');
const tempDir = path.join(uploadsDir, 'temp');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}
if (!fs.existsSync(tempDir)) {
  fs.mkdirSync(tempDir, { recursive: true });
}

// Multer configuration for ZIP uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, tempDir);
  },
  filename: (req, file, cb) => {
    const uniqueName = `${require('crypto').randomUUID()}-${Date.now()}${path.extname(file.originalname)}`;
    cb(null, uniqueName);
  }
});

const upload = multer({
  storage: storage,
  fileFilter: (req, file, cb) => {
    if (file.mimetype === 'application/zip' ||
        file.mimetype === 'application/x-zip-compressed' ||
        path.extname(file.originalname).toLowerCase() === '.zip') {
      cb(null, true);
    } else {
      cb(new Error('Only ZIP files are allowed'));
    }
  },
  limits: {
    fileSize: 100 * 1024 * 1024, // 100MB limit
  }
});

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Serve static files (uploaded images)
app.use('/uploads', express.static(path.join(__dirname, '../uploads')));

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Projects endpoints
app.get('/api/projects', async (req, res) => {
  try {
    const projects = await new Promise((resolve, reject) => {
      db.all('SELECT * FROM projects ORDER BY created_at DESC', (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    // Get stats for each project
    const projectsWithStats = await Promise.all(
      projects.map(async (project) => {
        const stats = await getProjectStats(project.id);
        return {
          ...project,
          classes: JSON.parse(project.classes),
          stats
        };
      })
    );

    res.json({ success: true, data: projectsWithStats });
  } catch (err) {
    console.error('Database error:', err);
    res.status(500).json({ success: false, error: 'Database error' });
  }
});

// Get single project
app.get('/api/projects/:id', async (req, res) => {
  try {
    const project = await new Promise((resolve, reject) => {
      db.get('SELECT * FROM projects WHERE id = ?', [req.params.id], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });

    if (!project) {
      return res.status(404).json({ success: false, error: 'Project not found' });
    }

    const stats = await getProjectStats(project.id);

    res.json({
      success: true,
      data: {
        ...project,
        classes: JSON.parse(project.classes),
        stats
      }
    });
  } catch (err) {
    console.error('Database error:', err);
    res.status(500).json({ success: false, error: 'Database error' });
  }
});

app.post('/api/projects', (req, res) => {
  const { name, description, classes } = req.body;

  if (!name || !classes || !Array.isArray(classes) || classes.length === 0) {
    return res.status(400).json({
      success: false,
      error: 'Name and classes are required'
    });
  }

  const id = require('crypto').randomUUID();
  const classesJson = JSON.stringify(classes);

  db.run(
    'INSERT INTO projects (id, name, description, classes) VALUES (?, ?, ?, ?)',
    [id, name, description || null, classesJson],
    function(err) {
      if (err) {
        console.error('Database error:', err);
        if (err.code === 'SQLITE_CONSTRAINT_UNIQUE') {
          return res.status(400).json({
            success: false,
            error: 'Project name already exists'
          });
        }
        return res.status(500).json({ success: false, error: 'Database error' });
      }

      // Return the created project
      db.get('SELECT * FROM projects WHERE id = ?', [id], (err, row) => {
        if (err) {
          console.error('Database error:', err);
          return res.status(500).json({ success: false, error: 'Database error' });
        }

        const project = {
          ...row,
          classes: JSON.parse(row.classes),
          stats: {
            total_images: 0,
            classified_images: 0,
            unclassified_images: 0,
            progress_percentage: 0
          }
        };

        res.status(201).json({
          success: true,
          data: project,
          message: 'Project created successfully'
        });
      });
    }
  );
});

// Upload ZIP file endpoint
app.post('/api/projects/:id/upload', upload.single('zipFile'), async (req, res) => {
  try {
    const projectId = req.params.id;

    // Check if project exists
    db.get('SELECT * FROM projects WHERE id = ?', [projectId], async (err, project) => {
      if (err) {
        console.error('Database error:', err);
        return res.status(500).json({ success: false, error: 'Database error' });
      }

      if (!project) {
        return res.status(404).json({ success: false, error: 'Project not found' });
      }

      if (!req.file) {
        return res.status(400).json({ success: false, error: 'No ZIP file uploaded' });
      }

      try {
        const result = await processZipFile(projectId, req.file.path);
        res.json({
          success: true,
          data: result,
          message: `Upload completed. Processed ${result.processed_files} of ${result.total_files} files.`
        });
      } catch (error) {
        console.error('Error processing ZIP file:', error);
        res.status(500).json({ success: false, error: 'Failed to process ZIP file' });
      }
    });
  } catch (error) {
    console.error('Error uploading ZIP file:', error);
    res.status(500).json({ success: false, error: 'Failed to upload ZIP file' });
  }
});

// Get project images
app.get('/api/images/project/:projectId', (req, res) => {
  const { projectId } = req.params;
  const { status } = req.query;

  let query = 'SELECT * FROM images WHERE project_id = ?';
  const params = [projectId];

  if (status) {
    query += ' AND status = ?';
    params.push(status);
  }

  query += ' ORDER BY created_at ASC';

  db.all(query, params, (err, rows) => {
    if (err) {
      console.error('Database error:', err);
      return res.status(500).json({ success: false, error: 'Database error' });
    }

    res.json({ success: true, data: rows });
  });
});

// Get images with classifications
app.get('/api/images/project/:projectId/with-classifications', (req, res) => {
  const { projectId } = req.params;

  const query = `
    SELECT
      i.*,
      c.id as classification_id,
      c.class_name,
      c.classified_at
    FROM images i
    LEFT JOIN classifications c ON i.id = c.image_id
    WHERE i.project_id = ?
    ORDER BY i.created_at ASC
  `;

  db.all(query, [projectId], (err, rows) => {
    if (err) {
      console.error('Database error:', err);
      return res.status(500).json({ success: false, error: 'Database error' });
    }

    const images = rows.map(row => {
      const image = {
        id: row.id,
        project_id: row.project_id,
        filename: row.filename,
        original_filename: row.original_filename,
        file_path: row.file_path,
        file_size: row.file_size,
        mime_type: row.mime_type,
        width: row.width,
        height: row.height,
        status: row.status,
        created_at: row.created_at
      };

      if (row.classification_id) {
        image.classification = {
          id: row.classification_id,
          image_id: row.id,
          project_id: row.project_id,
          class_name: row.class_name,
          classified_at: row.classified_at
        };
      }

      return image;
    });

    res.json({ success: true, data: images });
  });
});

// Classify image endpoint
app.post('/api/classifications', (req, res) => {
  const { image_id, class_name } = req.body;

  if (!image_id || !class_name) {
    return res.status(400).json({
      success: false,
      error: 'Image ID and class name are required'
    });
  }

  // Check if image exists
  db.get('SELECT * FROM images WHERE id = ?', [image_id], (err, image) => {
    if (err) {
      console.error('Database error:', err);
      return res.status(500).json({ success: false, error: 'Database error' });
    }

    if (!image) {
      return res.status(404).json({ success: false, error: 'Image not found' });
    }

    const classificationId = require('crypto').randomUUID();

    // Insert or update classification
    db.run(
      'INSERT OR REPLACE INTO classifications (id, image_id, project_id, class_name) VALUES (?, ?, ?, ?)',
      [classificationId, image_id, image.project_id, class_name],
      function(err) {
        if (err) {
          console.error('Database error:', err);
          return res.status(500).json({ success: false, error: 'Database error' });
        }

        // Update image status
        db.run(
          'UPDATE images SET status = ? WHERE id = ?',
          ['classified', image_id],
          (err) => {
            if (err) {
              console.error('Database error:', err);
              return res.status(500).json({ success: false, error: 'Database error' });
            }

            res.json({
              success: true,
              data: {
                id: classificationId,
                image_id,
                project_id: image.project_id,
                class_name,
                classified_at: new Date().toISOString()
              },
              message: 'Image classified successfully'
            });
          }
        );
      }
    );
  });
});

// Get project statistics
async function getProjectStats(projectId) {
  try {
    const totalResult = await new Promise((resolve, reject) => {
      db.get('SELECT COUNT(*) as count FROM images WHERE project_id = ?', [projectId], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });

    const classifiedResult = await new Promise((resolve, reject) => {
      db.get('SELECT COUNT(*) as count FROM images WHERE project_id = ? AND status = ?', [projectId, 'classified'], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });

    const total_images = totalResult?.count || 0;
    const classified_images = classifiedResult?.count || 0;
    const unclassified_images = total_images - classified_images;
    const progress_percentage = total_images > 0 ? Math.round((classified_images / total_images) * 100) : 0;

    return {
      total_images,
      classified_images,
      unclassified_images,
      progress_percentage
    };
  } catch (error) {
    console.error('Error getting project stats:', error);
    return {
      total_images: 0,
      classified_images: 0,
      unclassified_images: 0,
      progress_percentage: 0
    };
  }
}

// ZIP processing function
async function processZipFile(projectId, zipFilePath) {
  const result = {
    total_files: 0,
    processed_files: 0,
    skipped_files: 0,
    errors: []
  };

  try {
    // Create project directory
    const projectDir = path.join(__dirname, '../uploads/projects', projectId);
    if (!fs.existsSync(projectDir)) {
      fs.mkdirSync(projectDir, { recursive: true });
    }

    // Create temporary extraction directory
    const tempExtractDir = path.join(path.dirname(zipFilePath), `extract-${require('crypto').randomUUID()}`);
    fs.mkdirSync(tempExtractDir, { recursive: true });

    try {
      // Extract ZIP file
      await fs.createReadStream(zipFilePath)
        .pipe(unzipper.Extract({ path: tempExtractDir }))
        .promise();

      // Process extracted files
      const processDirectory = async (dirPath) => {
        const items = fs.readdirSync(dirPath);

        for (const item of items) {
          const itemPath = path.join(dirPath, item);
          const stats = fs.statSync(itemPath);

          if (stats.isDirectory()) {
            // Skip hidden directories
            if (!item.startsWith('.') && !item.startsWith('__MACOSX')) {
              await processDirectory(itemPath);
            }
          } else if (stats.isFile()) {
            result.total_files++;

            // Check if it's an image file
            const ext = path.extname(item).toLowerCase();
            const supportedExts = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];

            if (!supportedExts.includes(ext) || item.startsWith('.')) {
              result.skipped_files++;
              continue;
            }

            try {
              // Generate unique filename
              const uniqueFilename = `${require('crypto').randomUUID()}${ext}`;
              const finalPath = path.join(projectDir, uniqueFilename);

              // Copy file to project directory
              fs.copyFileSync(itemPath, finalPath);

              // Get file stats
              const fileStats = fs.statSync(finalPath);

              // Get image metadata
              let metadata = null;
              try {
                metadata = await sharp(finalPath).metadata();
              } catch (metaError) {
                console.warn('Could not get image metadata:', metaError);
              }

              // Determine MIME type
              let mimeType = 'image/jpeg'; // default
              switch (ext) {
                case '.png': mimeType = 'image/png'; break;
                case '.gif': mimeType = 'image/gif'; break;
                case '.bmp': mimeType = 'image/bmp'; break;
                case '.webp': mimeType = 'image/webp'; break;
              }

              // Create image record in database
              const imageId = require('crypto').randomUUID();
              const relativePath = path.relative(path.join(__dirname, '../uploads'), finalPath);

              await new Promise((resolve, reject) => {
                db.run(
                  'INSERT INTO images (id, project_id, filename, original_filename, file_path, file_size, mime_type, width, height) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)',
                  [
                    imageId,
                    projectId,
                    uniqueFilename,
                    item,
                    relativePath,
                    fileStats.size,
                    mimeType,
                    metadata?.width || null,
                    metadata?.height || null
                  ],
                  function(err) {
                    if (err) reject(err);
                    else resolve();
                  }
                );
              });

              result.processed_files++;
            } catch (error) {
              console.error('Error processing image file:', error);
              result.errors.push(`${item}: ${error.message}`);
            }
          }
        }
      };

      await processDirectory(tempExtractDir);

    } finally {
      // Clean up temporary extraction directory
      try {
        fs.rmSync(tempExtractDir, { recursive: true, force: true });
      } catch (error) {
        console.error('Error cleaning up temp extraction directory:', error);
      }
    }

  } catch (error) {
    const errorMessage = error.message || 'Unknown error';
    result.errors.push(`ZIP extraction failed: ${errorMessage}`);
  } finally {
    // Clean up uploaded ZIP file
    try {
      if (fs.existsSync(zipFilePath)) {
        fs.unlinkSync(zipFilePath);
      }
    } catch (error) {
      console.error('Error cleaning up ZIP file:', error);
    }
  }

  return result;
}

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(500).json({
    error: 'Internal server error',
    message: err.message
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ error: 'Route not found' });
});

// Start server
app.listen(PORT, () => {
  console.log(`Server running on http://localhost:${PORT}`);
  console.log('Database initialized successfully');
});
