{"name": "backend", "version": "1.0.0", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.js", "build": "tsc", "start": "node dist/index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"archiver": "^7.0.1", "cors": "^2.8.5", "express": "^4.21.2", "multer": "^2.0.1", "sharp": "^0.34.2", "sqlite3": "^5.1.7", "unzipper": "^0.12.3", "uuid": "^11.1.0"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/multer": "^1.4.13", "@types/node": "^24.0.4", "@types/uuid": "^10.0.0", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}